import requests
import json
import os # Import the os module

# Initialize with environment variable, but allow setting it programmatically
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY") # Fetch API key from environment variable
OPENROUTER_API_BASE = "https://openrouter.ai/api/v1"

def set_openrouter_api_key(api_key):
    """Set the OpenRouter API key programmatically"""
    global OPENROUTER_API_KEY
    OPENROUTER_API_KEY = api_key
    return OPENROUTER_API_KEY is not None and OPENROUTER_API_KEY != ""

def analyze_text_with_openrouter(
    user_content: str, # The complete content for the user's message
    model_name: str = "openai/gpt-3.5-turbo",
    system_message_content: str = "You are a helpful AI assistant.",
    site_url: str = "http://example.com"
):
    """
    Sends the provided user content and system message to an OpenRouter model.
    """
    if not OPENROUTER_API_KEY:
        print("Error: OpenRouter API key not set in ai_interface.py")
        return "Error: API key not configured."

    print(f"Using OpenRouter API key (first 4 chars): {OPENROUTER_API_KEY[:4]}...")
    print(f"Using model: {model_name}")

    headers = {
        "Authorization": f"Bearer {OPENROUTER_API_KEY}",
        "Content-Type": "application/json",
        "HTTP-Referer": site_url, # Optional, but good practice
        "X-Title": "Web Domain Analyzer" # Optional
    }

    data = {
        "model": model_name,
        "messages": [
            {"role": "system", "content": system_message_content},
            {"role": "user", "content": user_content} # Remove truncation to use full context
        ],
    }

    try:
        print("Sending request to OpenRouter API...")
        response = requests.post(f"{OPENROUTER_API_BASE}/chat/completions", headers=headers, data=json.dumps(data), timeout=120)

        # Print the status code
        print(f"OpenRouter API response status code: {response.status_code}")

        # Raise an exception for 4xx/5xx status codes
        response.raise_for_status()

        # Parse the JSON response
        json_response = response.json()
        print("Successfully received response from OpenRouter API")

        # Debug: Print the full response structure
        print("=== FULL API RESPONSE DEBUG ===")
        print(f"Response keys: {list(json_response.keys())}")
        if 'choices' in json_response:
            print(f"Number of choices: {len(json_response['choices'])}")
            if len(json_response['choices']) > 0:
                choice = json_response['choices'][0]
                print(f"Choice keys: {list(choice.keys())}")
                if 'message' in choice:
                    message = choice['message']
                    print(f"Message keys: {list(message.keys())}")
                    if 'content' in message:
                        content = message['content']
                        print(f"Content type: {type(content)}")
                        print(f"Content is None: {content is None}")
                        print(f"Content is empty string: {content == ''}")
                        print(f"Content length: {len(content) if content else 'N/A'}")
                        if content:
                            print(f"Content preview (first 100 chars): {content[:100]}")
                    else:
                        print("No 'content' key in message")
                else:
                    print("No 'message' key in choice")
            else:
                print("No choices in response")
        else:
            print("No 'choices' key in response")
        print("=== END FULL API RESPONSE DEBUG ===")

        # Extract and return the content
        content = json_response['choices'][0]['message']['content']
        print(f"Response length: {len(content)} characters")
        return content
    except requests.exceptions.RequestException as e:
        print(f"Error calling OpenRouter API: {e}")
        if 'response' in locals() and response:
            print(f"Response status code: {response.status_code}")
            print(f"Response content: {response.text}")
        return f"Error during API call: {e}"
    except (KeyError, IndexError) as e:
        print(f"Error parsing OpenRouter response: {e}")
        if 'response' in locals() and response:
            print(f"Response content: {response.text}")
        else:
            print("No response received")
        return "Error parsing API response."

# Placeholder for Ollama interaction (would typically use the 'ollama' library)
def query_ollama_with_context(model_name, query, context_chunks):
    """
    Conceptual: Sends a query and context to a local Ollama instance.
    Requires Ollama to be running and the 'ollama' Python library installed.
    """
    # import ollama
    # combined_context = "\n".join(context_chunks)
    # prompt = f"Based on the following context:\n{combined_context}\n\nAnswer the question: {query}"
    # try:
    #   response = ollama.chat(model=model_name, messages=[{'role': 'user', 'content': prompt}])
    #   return response['message']['content']
    # except Exception as e:
    #   print(f"Error querying Ollama: {e}")
    #   return "Error querying Ollama."
    print(f"Conceptual Ollama call: Model={model_name}, Query='{query}', Context Chunks Count={len(context_chunks)}")
    return "Placeholder Ollama response. Implement with 'ollama' library."
