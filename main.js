const { app, BrowserWindow, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const readline = require('readline');
const fs = require('fs');
// We'll use dynamic import for fetch
const ConversationManager = require('./conversation_manager');

let mainWindow;
let pythonProcess = null;
let currentInputCallback = null;
let conversationManager = null;
let activeSessionId = null;

// User settings
let userSettings = {
  defaultAnalysisMethod: 'rag-only',
  defaultModel: 'mistralai/mistral-7b-instruct',
  autoSwitchToRag: true,
  openrouterApiKey: '',
  deepResearchEnabled: false,
  deepResearchResultsCount: 3,
  deepResearchDefaultResultsPerQuery: 5, // Default number of results per search query
  deepResearchMaxSubQueries: 5, // Maximum number of sub-queries for multi-query research
  deepResearchUrlTimeout: 10000, // Timeout per URL in milliseconds (10 seconds)
  deepResearchSaveToDatabase: false,
  autoSaveOperationSummaries: false, // Whether to automatically save operation summaries to the RAG system
  // Pinecone settings
  pineconeApiKey: '',
  pineconeEnvironment: '',
  pineconeIndexName: '',
  usePinecone: false
};

// System state object to track application state
const systemState = {
  databaseStatus: 'initializing', // 'initializing', 'ready', 'error'
  ragIndexStatus: 'not_built', // 'not_built', 'building', 'ready', 'error'
  lastToolUsed: null, // 'rag_query', 'deep_research', etc.
  lastQueryTime: null,
  lastRelevanceScore: null,
  sessionHistory: [], // Array of recent queries and tools used
  systemMessages: [] // Important system messages to relay to the LLM
};

// Checkpoint state to track the current operation for error recovery
const checkpointState = {
  currentOperation: null, // 'rag_query', 'deep_research', 'validation', etc.
  currentStep: null, // 'initialization', 'data_retrieval', 'analysis', 'validation', etc.
  originalQuery: null, // The original user query
  operationStartTime: null, // When the operation started
  stepStartTime: null, // When the current step started
  attempts: {}, // Track retry attempts for each step
  parameters: {}, // Store parameters needed for retry
  errors: [], // Track errors encountered
  results: {}, // Store intermediate results
  model: null, // The LLM model being used
  summaryGenerated: false // Whether a summary has been generated for this operation
};

// Global API cooldown system to prevent rate limiting
const apiCooldown = {
  lastApiCallTime: 0,
  minCooldownPeriod: 30000, // 30 seconds between API calls (doubled from 15 seconds)

  // Function to ensure cooldown period is respected
  async ensureCooldown() {
    const now = Date.now();
    const timeSinceLastCall = now - this.lastApiCallTime;

    if (timeSinceLastCall < this.minCooldownPeriod) {
      const waitTime = this.minCooldownPeriod - timeSinceLastCall;
      console.log(`Respecting API cooldown, waiting ${waitTime/1000} seconds before making API call`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    this.lastApiCallTime = Date.now();
    return true;
  },

  // Update the last API call time
  updateLastCallTime() {
    this.lastApiCallTime = Date.now();
  }
};

// Function to update system state
function updateSystemState(key, value) {
  systemState[key] = value;
  console.log(`System state updated: ${key} = ${value}`);

  // If this is an important update, add it to systemMessages
  if (['databaseStatus', 'ragIndexStatus'].includes(key) ||
      (key === 'lastRelevanceScore' && value < 5)) {
    systemState.systemMessages.push({
      timestamp: new Date().toISOString(),
      type: key,
      message: `${key} changed to ${value}`
    });

    // Keep only the last 5 messages
    if (systemState.systemMessages.length > 5) {
      systemState.systemMessages.shift();
    }
  }
}

// Function to generate system context for LLM prompts
function generateSystemContext() {
  // Create a formatted string with relevant system state
  let context = '--- SYSTEM STATE ---\n';

  // Add database and RAG index status
  context += `Database: ${systemState.databaseStatus}\n`;
  context += `RAG Index: ${systemState.ragIndexStatus}\n`;

  // Add recent query information if available
  if (systemState.lastQueryTime) {
    const timeDiff = (new Date() - new Date(systemState.lastQueryTime)) / 1000;
    context += `Last query: ${Math.round(timeDiff)} seconds ago\n`;

    if (systemState.lastRelevanceScore !== null) {
      context += `Last relevance score: ${systemState.lastRelevanceScore}/10\n`;
    }

    if (systemState.lastToolUsed) {
      context += `Last tool used: ${systemState.lastToolUsed}\n`;
    }
  }

  // Add important system messages
  if (systemState.systemMessages.length > 0) {
    context += '\n--- RECENT SYSTEM MESSAGES ---\n';
    systemState.systemMessages.forEach(msg => {
      context += `[${new Date(msg.timestamp).toLocaleTimeString()}] ${msg.message}\n`;
    });
  }

  context += '------------------------\n\n';
  return context;
}

// Function to enhance prompts with system context
function enhancePromptWithSystemContext(originalPrompt) {
  const systemContext = generateSystemContext();
  return `${systemContext}${originalPrompt}`;
}

// Function to start a new checkpoint operation
function startCheckpointOperation(operation, query, model, parameters = {}) {
  checkpointState.currentOperation = operation;
  checkpointState.currentStep = 'initialization';
  checkpointState.originalQuery = query;
  checkpointState.operationStartTime = new Date().toISOString();
  checkpointState.stepStartTime = new Date().toISOString();
  checkpointState.attempts = {};
  checkpointState.parameters = parameters;
  checkpointState.errors = [];
  checkpointState.results = {};
  checkpointState.model = model;
  checkpointState.summaryGenerated = false;

  console.log(`Starting checkpoint operation: ${operation} for query: ${query}`);
}

// Function to update the current checkpoint step
function updateCheckpointStep(step) {
  checkpointState.currentStep = step;
  checkpointState.stepStartTime = new Date().toISOString();

  // Initialize attempt counter for this step if it doesn't exist
  if (!checkpointState.attempts[step]) {
    checkpointState.attempts[step] = 0;
  }

  console.log(`Checkpoint step updated: ${step}`);
}

// Function to record an error in the checkpoint state
function recordCheckpointError(error, step = checkpointState.currentStep) {
  // Increment attempt counter for this step
  checkpointState.attempts[step] = (checkpointState.attempts[step] || 0) + 1;

  // Add error to the errors array
  checkpointState.errors.push({
    timestamp: new Date().toISOString(),
    step: step,
    error: typeof error === 'string' ? error : error.message || 'Unknown error',
    attempt: checkpointState.attempts[step]
  });

  console.log(`Checkpoint error recorded for step ${step}, attempt ${checkpointState.attempts[step]}: ${error}`);
}

// Function to store a result in the checkpoint state
function storeCheckpointResult(key, value) {
  checkpointState.results[key] = value;
  console.log(`Checkpoint result stored: ${key}`);
}

// Function to generate a checkpoint context for error recovery
function generateCheckpointContext() {
  return {
    operation: checkpointState.currentOperation,
    step: checkpointState.currentStep,
    query: checkpointState.originalQuery,
    attempts: checkpointState.attempts[checkpointState.currentStep] || 0,
    errors: checkpointState.errors,
    results: checkpointState.results,
    parameters: checkpointState.parameters
  };
}

// Function to handle error recovery with the LLM
async function recoverFromError(error, context = null) {
  try {
    // If no context is provided, generate it from the checkpoint state
    if (!context) {
      context = generateCheckpointContext();
    }

    // Record the error in the checkpoint state
    recordCheckpointError(error);

    console.log('Attempting error recovery with LLM...');

    // Create a recovery prompt for the LLM
    const recoveryPrompt = `
You are an AI assistant helping to recover from an error in a multi-step operation.

CURRENT OPERATION: ${context.operation}
CURRENT STEP: ${context.step}
ORIGINAL QUERY: "${context.query}"
ATTEMPT: ${context.attempts}

ERROR: ${error.message || error}

Previous errors in this operation:
${context.errors.map(e => `- [${e.step}] ${e.error}`).join('\n')}

Available results from previous steps:
${Object.entries(context.results).map(([key, value]) => `- ${key}: ${typeof value === 'string' ? value : JSON.stringify(value)}`).join('\n')}

Parameters:
${Object.entries(context.parameters).map(([key, value]) => `- ${key}: ${typeof value === 'string' ? value : JSON.stringify(value)}`).join('\n')}

Please analyze this error and provide guidance on how to recover. You can:
1. Suggest retrying the current step with modified parameters
2. Suggest skipping to a different step
3. Suggest an alternative approach to complete the operation

Respond in this format:
RECOVERY_ACTION: [retry_step, skip_to_step, alternative_approach, abort]
STEP_TO_EXECUTE: [step name or "none"]
MODIFIED_PARAMETERS: [JSON object with modified parameters or "none"]
REASONING: [Your detailed reasoning]
INSTRUCTIONS: [Clear instructions for the system]
`;

    // Get the API key
    const apiKey = process.env.OPENROUTER_API_KEY || userSettings.openrouterApiKey;
    if (!apiKey) {
      console.log('No API key available for error recovery');
      return { action: 'abort', reason: 'No API key available' };
    }

    // Call the LLM with the recovery prompt and retry logic
    console.log('Calling LLM for error recovery with retry logic');

    // Ensure we respect the API cooldown
    console.log('Ensuring API cooldown period is respected before error recovery...');
    await apiCooldown.ensureCooldown();

    const { default: fetch } = await import('node-fetch');

    // Retry configuration with much longer delays
    const maxRetries = 3;
    let retries = 0;
    let delay = 10000; // Start with 10 seconds delay (5x the original)
    let llmResponse;

    while (retries <= maxRetries) {
      try {
        llmResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`,
            'HTTP-Referer': 'http://localhost:3000',
            'X-Title': 'Fungi Error Recovery'
          },
          body: JSON.stringify({
            model: context.model || userSettings.defaultModel,
            messages: [
              { role: 'user', content: recoveryPrompt }
            ]
          })
        });

        if (llmResponse.ok) {
          // Success, break out of retry loop
          console.log('LLM API call for error recovery successful');
          break;
        } else if (llmResponse.status === 429) {
          // Rate limit hit, retry with exponential backoff
          retries++;
          if (retries > maxRetries) {
            console.error(`Rate limit hit, max retries (${maxRetries}) exceeded`);
            return { action: 'abort', reason: 'Rate limit exceeded during recovery attempt' };
          }

          console.log(`Rate limit hit (429), retrying in ${delay/1000} seconds... (Attempt ${retries} of ${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, delay));
          delay *= 2; // Exponential backoff
        } else {
          // Other error, don't retry
          console.error(`LLM API error during recovery: ${llmResponse.status}`);
          return { action: 'abort', reason: `API error: ${llmResponse.status}` };
        }
      } catch (error) {
        // Network error or other exception
        retries++;
        if (retries > maxRetries) {
          console.error(`API call failed during recovery, max retries (${maxRetries}) exceeded:`, error);
          return { action: 'abort', reason: 'Network error during recovery attempt' };
        }

        console.log(`API call failed during recovery, retrying in ${delay/1000} seconds... (Attempt ${retries} of ${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, delay));
        delay *= 2; // Exponential backoff
      }
    }

    if (!llmResponse || !llmResponse.ok) {
      console.error(`LLM API error after retries during recovery`);
      return { action: 'abort', reason: 'API error after retries' };
    }

    const recoveryResult = await llmResponse.json();
    const recoveryContent = recoveryResult.choices[0].message.content;

    console.log('Recovery guidance received:', recoveryContent);

    // Parse the recovery response
    const recovery = parseRecoveryResponse(recoveryContent);

    // Return the parsed recovery plan
    return recovery;
  } catch (error) {
    console.error('Error during recovery process:', error);
    return { action: 'abort', reason: 'Error during recovery process' };
  }
}

// Helper function to parse the recovery response
function parseRecoveryResponse(recoveryContent) {
  const result = {
    action: 'abort', // Default to abort if we can't parse
    step: null,
    parameters: null,
    reasoning: '',
    instructions: ''
  };

  // Extract values using regex
  const actionMatch = recoveryContent.match(/RECOVERY_ACTION:\s*(\w+)/i);
  if (actionMatch) {
    const action = actionMatch[1].toLowerCase();
    if (['retry_step', 'skip_to_step', 'alternative_approach', 'abort'].includes(action)) {
      result.action = action;
    }
  }

  const stepMatch = recoveryContent.match(/STEP_TO_EXECUTE:\s*([a-zA-Z_]+|none)/i);
  if (stepMatch && stepMatch[1].toLowerCase() !== 'none') {
    result.step = stepMatch[1];
  }

  const paramsMatch = recoveryContent.match(/MODIFIED_PARAMETERS:\s*({[\s\S]*?}|none)/i);
  if (paramsMatch && paramsMatch[1].toLowerCase() !== 'none') {
    try {
      // Try to parse the JSON parameters
      const paramsStr = paramsMatch[1].trim();
      result.parameters = JSON.parse(paramsStr);
    } catch (error) {
      console.error('Error parsing modified parameters:', error);
    }
  }

  const reasoningMatch = recoveryContent.match(/REASONING:\s*([\s\S]*?)(?=INSTRUCTIONS:|$)/i);
  if (reasoningMatch) {
    result.reasoning = reasoningMatch[1].trim();
  }

  const instructionsMatch = recoveryContent.match(/INSTRUCTIONS:\s*([\s\S]*?)$/i);
  if (instructionsMatch) {
    result.instructions = instructionsMatch[1].trim();
  }

  return result;
}

// Function to generate an operation summary and store it in the knowledge base
async function generateOperationSummary() {
  try {
    if (checkpointState.summaryGenerated) {
      console.log('Summary already generated for this operation');
      return;
    }

    console.log('Generating operation summary...');

    // Create a summary prompt for the LLM
    const summaryPrompt = `
You are an AI assistant summarizing a completed operation for future learning.

OPERATION: ${checkpointState.currentOperation}
QUERY: "${checkpointState.originalQuery}"
DURATION: ${calculateDuration(checkpointState.operationStartTime, new Date().toISOString())}

Steps executed:
${Object.entries(checkpointState.attempts).map(([step, attempts]) =>
  `- ${step} (${attempts} attempt${attempts !== 1 ? 's' : ''})`
).join('\n')}

Errors encountered:
${checkpointState.errors.map(e => `- [${e.step}] ${e.error}`).join('\n') || 'None'}

Results:
${Object.entries(checkpointState.results).map(([key, value]) =>
  `- ${key}: ${typeof value === 'string' ? value.substring(0, 100) + (value.length > 100 ? '...' : '') : 'Complex result'}`
).join('\n')}

Please create a concise summary of this operation that would be helpful for future similar operations.
Include what worked, what didn't work, and any insights that would be valuable.

Respond in this format:
TITLE: [A descriptive title for this operation summary]
OPERATION_TYPE: ${checkpointState.currentOperation}
QUERY_TYPE: [Type of query this represents]
SUMMARY: [Concise summary of what happened]
KEY_INSIGHTS: [Bullet points of key insights]
RECOMMENDED_APPROACH: [Recommended approach for similar queries in the future]
`;

    // Get the API key
    const apiKey = process.env.OPENROUTER_API_KEY || userSettings.openrouterApiKey;
    if (!apiKey) {
      console.log('No API key available for summary generation');
      return;
    }

    // Call the LLM with the summary prompt
    console.log('Calling LLM for operation summary');

    // Ensure we respect the API cooldown
    console.log('Ensuring API cooldown period is respected before generating summary...');
    await apiCooldown.ensureCooldown();

    const { default: fetch } = await import('node-fetch');

    const llmResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'HTTP-Referer': 'http://localhost:3000',
        'X-Title': 'Fungi Operation Summary'
      },
      body: JSON.stringify({
        model: checkpointState.model || userSettings.defaultModel,
        messages: [
          { role: 'user', content: summaryPrompt }
        ]
      })
    });

    if (!llmResponse.ok) {
      console.error(`LLM API error during summary generation: ${llmResponse.status}`);
      return;
    }

    const summaryResult = await llmResponse.json();
    const summaryContent = summaryResult.choices[0].message.content;

    console.log('Operation summary generated:', summaryContent);

    // Mark summary as generated
    checkpointState.summaryGenerated = true;

    // Check if we should automatically save the summary to the knowledge base
    if (userSettings.autoSaveOperationSummaries) {
      console.log('Auto-saving operation summary to knowledge base (enabled in settings)');
      await storeOperationSummaryInKnowledgeBase(summaryContent);
    } else {
      console.log('Not saving operation summary to knowledge base (disabled in settings)');
    }

    return summaryContent;
  } catch (error) {
    console.error('Error generating operation summary:', error);
  }
}

// Helper function to store the operation summary in the knowledge base
async function storeOperationSummaryInKnowledgeBase(summary) {
  try {
    console.log('Storing operation summary in knowledge base...');

    // Create a temporary file with the summary
    const fs = require('fs');
    const os = require('os');
    const path = require('path');
    const tempDir = os.tmpdir();
    const tempFile = path.join(tempDir, `operation_summary_${Date.now()}.txt`);

    fs.writeFileSync(tempFile, summary);

    // Extract a title from the summary
    const titleMatch = summary.match(/TITLE:\s*(.*?)(?:\n|$)/i);
    const title = titleMatch ? titleMatch[1].trim() : 'Operation Summary';

    // Add to the RAG system
    const addResult = await runPythonCommand([
      'add_document.py',
      tempFile,
      'operation_summary',
      title
    ]);

    console.log('Operation summary stored in knowledge base');

    // Clean up the temporary file
    fs.unlinkSync(tempFile);

    return true;
  } catch (error) {
    console.error('Error storing operation summary in knowledge base:', error);
    return false;
  }
}

// Helper function to calculate duration between two ISO timestamps
function calculateDuration(startTime, endTime) {
  const start = new Date(startTime);
  const end = new Date(endTime);
  const durationMs = end - start;

  // Format as minutes and seconds
  const minutes = Math.floor(durationMs / 60000);
  const seconds = Math.floor((durationMs % 60000) / 1000);

  return `${minutes}m ${seconds}s`;
}

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
    },
    backgroundColor: '#050510',
    autoHideMenuBar: true
  });

  mainWindow.loadFile('index.html');

  // Uncomment to open DevTools in development
  // mainWindow.webContents.openDevTools();

  mainWindow.on('closed', function () {
    mainWindow = null;
    if (pythonProcess) {
      pythonProcess.kill();
    }
  });

  // Set up system state listeners after window is created
  setupSystemStateListeners();
}

// Set up event listeners for system state updates
function setupSystemStateListeners() {
  // Database status events
  ipcMain.on('database-status', (event, status) => {
    updateSystemState('databaseStatus', status);
  });

  // RAG index events
  ipcMain.on('rag-index-building', (event, isBuilding) => {
    updateSystemState('ragIndexStatus', isBuilding ? 'building' : 'ready');
  });

  // Error events
  ipcMain.on('python-error', (event, error) => {
    systemState.systemMessages.push({
      timestamp: new Date().toISOString(),
      type: 'error',
      message: `Python error: ${error}`
    });
  });

  // Process exit events
  ipcMain.on('python-exit', (event, code) => {
    if (code !== 0) {
      updateSystemState('databaseStatus', 'error');
      updateSystemState('ragIndexStatus', 'error');
    }
  });

  // Tool switching events
  ipcMain.on('tool-switching', (event, data) => {
    updateSystemState('lastToolUsed', data.to);
    systemState.systemMessages.push({
      timestamp: new Date().toISOString(),
      type: 'tool_switch',
      message: `Switched from ${data.from} to ${data.to}: ${data.reason}`
    });
  });

  // Relevance score events
  mainWindow.webContents.on('rag-relevance-checked', (data) => {
    updateSystemState('lastRelevanceScore', data.score);
  });
}

// IPC handlers for communication with renderer process
ipcMain.handle('select-file', async () => {
  const result = await dialog.showOpenDialog(mainWindow, {
    properties: ['openFile'],
    filters: [
      { name: 'ChromeDriver', extensions: ['exe'] },
      { name: 'All Files', extensions: ['*'] }
    ]
  });

  if (!result.canceled) {
    return result.filePaths[0];
  }
  return null;
});

// Select documents for import
ipcMain.handle('select-documents', async () => {
  const result = await dialog.showOpenDialog(mainWindow, {
    properties: ['openFile', 'multiSelections'],
    filters: [
      { name: 'Documents', extensions: ['pdf', 'docx', 'doc', 'csv', 'xlsx', 'xls', 'txt'] },
      { name: 'PDF Files', extensions: ['pdf'] },
      { name: 'Word Documents', extensions: ['docx', 'doc'] },
      { name: 'CSV Files', extensions: ['csv'] },
      { name: 'Excel Files', extensions: ['xlsx', 'xls'] },
      { name: 'Text Files', extensions: ['txt'] },
      { name: 'All Files', extensions: ['*'] }
    ]
  });

  if (!result.canceled) {
    return result.filePaths;
  }
  return [];
});

// Import documents
ipcMain.handle('import-documents', async (event, filePaths, options) => {
  try {
    // Kill any existing Python process
    if (pythonProcess) {
      console.log('Killing existing Python process');
      pythonProcess.kill();
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Prepare arguments for main.py
    const args = ['main.py', '--import-documents'];

    // Add options if provided
    if (options) {
      args.push(`--options=${JSON.stringify(options)}`);
    }

    // Add file paths
    args.push(...filePaths);

    // Send log to renderer
    mainWindow.webContents.send('log', 'Starting document import process...');
    console.log('Starting Python process for document import');

    // Spawn Python process
    const importProcess = spawn('python', args);

    // Collect output
    let output = '';
    let errorOutput = '';

    // Set up promise to wait for process to complete
    const importResult = new Promise((resolve, reject) => {
      importProcess.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;
        mainWindow.webContents.send('log', text);
      });

      importProcess.stderr.on('data', (data) => {
        const text = data.toString();
        errorOutput += text;
        console.error(`Python stderr: ${text}`);
        mainWindow.webContents.send('log', `Error: ${text}`);
      });

      importProcess.on('close', (code) => {
        if (code === 0) {
          resolve(output);
        } else {
          reject(new Error(`Process exited with code ${code}: ${errorOutput}`));
        }
      });
    });

    // Wait for the process to complete
    const result = await importResult;

    // Parse the output to determine if documents were imported
    const importMatch = result.match(/documents_imported:(\d+)/);
    if (importMatch) {
      const count = parseInt(importMatch[1]);
      console.log(`Imported ${count} documents`);

      // Extract document info
      const docRegex = /document:(\d+):([^:]+):([^:]+)/g;
      const documents = [];
      let match;
      while ((match = docRegex.exec(result)) !== null) {
        documents.push({
          id: match[1],
          filename: match[2],
          type: match[3]
        });
      }

      // Notify the renderer
      mainWindow.webContents.send('documents-imported', { count, documents });

      return { success: true, count, documents };
    }

    return { success: false, error: 'Failed to parse import results' };
  } catch (error) {
    console.error('Error importing documents:', error.message);
    mainWindow.webContents.send('log', `Error: ${error.message}`);
    throw new Error(`Failed to import documents: ${error.message}`);
  }
});

// Run main.py with parameters
ipcMain.handle('start-crawl', async (event, data) => {
  try {
    // Kill any existing Python process
    if (pythonProcess) {
      console.log('Killing existing Python process');
      pythonProcess.kill();
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Prepare arguments for main.py
    const args = ['main.py'];

    // Send log to renderer
    mainWindow.webContents.send('log', 'Starting Python process...');
    console.log('Starting Python process with main.py');

    // Spawn Python process with stdio options
    pythonProcess = spawn('python', args, {
      stdio: ['pipe', 'pipe', 'pipe'],
      windowsHide: false
    });

    // Set up data handling
    setupPythonProcessHandlers(pythonProcess);

    // Notify the renderer that the Python process has started
    mainWindow.webContents.send('python-start');

    // Wait for the initial prompt from main.py
    console.log('Waiting for initial prompt...');
    await new Promise(resolve => {
      setTimeout(resolve, 1500);
    });

    // Handle the initial choice prompt (1. Start a new web crawl, 2. Chat with existing data)
    console.log('Sending initial choice: 1 (Start a new web crawl)');
    await sendToPython('1');

    // Wait a bit before sending the URL
    await new Promise(resolve => setTimeout(resolve, 800));

    // Send the URL to the Python process
    console.log(`Sending URL: ${data.url}`);
    await sendToPython(data.url);

    // Wait a bit before sending the next input
    await new Promise(resolve => setTimeout(resolve, 800));

    // Send the max pages to the Python process
    console.log(`Sending max pages: ${data.max_pages}`);
    await sendToPython(data.max_pages.toString());

    // The ChromeDriver path is handled by a separate listener (chromeDriverPromptHandler)
    // We don't need to send it here, as it will be sent when the Python process prompts for it

    // Set up a one-time listener for the allowed paths prompt
    const allowedPathsPromptHandler = (stdoutData) => {
      const output = stdoutData.toString().trim();

      // Check if this is the allowed paths prompt
      if (output.includes('Enter allowed paths') ||
          output.includes('Enter allowed subdomain paths')) {

        console.log('Detected allowed paths prompt, sending response');

        // Small delay before responding
        setTimeout(() => {
          try {
            // Format and send the allowed paths
            if (data.allowed_paths && data.allowed_paths.length > 0) {
              // Format the allowed paths as a JSON string
              const allowedPathsStr = JSON.stringify(data.allowed_paths);
              console.log(`Sending allowed paths: ${allowedPathsStr}`);
              mainWindow.webContents.send('log', `Using ${data.allowed_paths.length} allowed subdomain paths for crawling`);

              // Send the allowed paths to the Python process
              pythonProcess.stdin.write(allowedPathsStr + '\n');
            } else {
              // If no allowed paths, send an empty string (press Enter)
              console.log('No allowed paths specified, sending empty input');
              pythonProcess.stdin.write('\n');
              mainWindow.webContents.send('log', 'Crawling all paths in the domain');
            }
          } catch (err) {
            console.error('Error sending allowed paths:', err);
          }

          // Remove the listener after handling
          pythonProcess.stdout.removeListener('data', allowedPathsPromptHandler);
        }, 500);
      }
    };

    // Add the listener for allowed paths prompt
    pythonProcess.stdout.on('data', allowedPathsPromptHandler);

    // Wait longer before sending the ChromeDriver path
    // This is the step that was getting stuck
    await new Promise(resolve => setTimeout(resolve, 2000));

    // We'll handle the ChromeDriver path differently
    // Instead of sending it now, we'll wait for the specific prompt
    // and set up a flag to indicate we're waiting for it
    console.log('Will handle ChromeDriver path when prompted');

    // Store the driver path for later use
    const driverPath = data.driver_path && data.driver_path.trim() !== ''
      ? data.driver_path.trim()
      : '';

    // Set up a one-time listener for the ChromeDriver prompt
    const chromeDriverPromptHandler = (data) => {
      const output = data.toString().trim();

      // Check if this is the ChromeDriver prompt
      if (output.includes('Enter path to ChromeDriver') ||
          output.includes('press Enter to use automatic download')) {

        console.log('Detected ChromeDriver prompt, sending response');

        // Small delay before responding
        setTimeout(() => {
          try {
            if (driverPath) {
              console.log(`Sending ChromeDriver path: ${driverPath}`);
              pythonProcess.stdin.write(driverPath + '\n');
            } else {
              console.log('Sending empty input for ChromeDriver path');
              pythonProcess.stdin.write('\n');
            }

            // Log success to UI
            mainWindow.webContents.send('log', driverPath
              ? `Using ChromeDriver from: ${driverPath}`
              : 'Using default webdriver-manager for ChromeDriver');

          } catch (err) {
            console.error('Error sending ChromeDriver path:', err);
          }
        }, 500);

        // Remove the listener after handling
        pythonProcess.stdout.removeListener('data', chromeDriverPromptHandler);
      }
    };

    // Add the listener
    pythonProcess.stdout.on('data', chromeDriverPromptHandler);

    // Return success
    console.log('Crawl started successfully');
    return { success: true };
  } catch (error) {
    console.error('Error starting crawl:', error.message);
    mainWindow.webContents.send('log', `Error: ${error.message}`);
    throw new Error(error.message);
  }
});

// Direct analysis has been removed

// Handle chat with existing data (direct option from main menu)
ipcMain.handle('start-chat', async (event) => {
  try {
    // Kill any existing Python process
    if (pythonProcess) {
      console.log('Killing existing Python process');
      pythonProcess.kill();
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    console.log('Starting chat with existing data...');
    mainWindow.webContents.send('log', 'Starting chat with existing data...');

    // Start Python process
    pythonProcess = spawn('python', ['main.py']);

    // Set up data handling
    setupPythonProcessHandlers(pythonProcess);

    // Notify the renderer that the Python process has started
    mainWindow.webContents.send('python-start');

    // Wait for the initial prompt
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Send option 2 (Chat with existing data)
    console.log('Sending initial choice: 2 (Chat with existing data)');
    await sendToPython('2');

    // Set up a flag to track if we've sent the RAG choice
    let ragChoiceSent = false;

    // Set up a one-time listener for the analysis choice prompt
    const analysisChoiceHandler = (data) => {
      const output = data.toString().trim();

      // Check if this is the analysis choice prompt and we haven't sent the choice yet
      if (!ragChoiceSent &&
          (output.includes('Choose analysis method:') ||
           (output.includes('1. Direct analysis') && output.includes('2. RAG-based Q&A')))) {

        console.log('Detected analysis choice prompt, automatically selecting RAG (option 2)');

        // Set the flag to prevent duplicate sends
        ragChoiceSent = true;

        // Small delay before responding
        setTimeout(() => {
          try {
            // Select RAG-only option (3) - faster and doesn't require API key
            pythonProcess.stdin.write('3\n');
            console.log('Automatically selected RAG-only (option 3)');

            // Notify the UI
            mainWindow.webContents.send('log', 'Automatically selected RAG-based Q&A for chat');
            mainWindow.webContents.send('python-output', 'Setting up RAG system with existing data...');
            mainWindow.webContents.send('rag-index-building', true);

            // Remove the listener after handling
            pythonProcess.stdout.removeListener('data', analysisChoiceHandler);
          } catch (err) {
            console.error('Error sending analysis choice:', err);
          }
        }, 500);
      }
    };

    // Add the listener
    pythonProcess.stdout.on('data', analysisChoiceHandler);

    // Return success
    return { success: true };
  } catch (error) {
    console.error('Error starting chat with existing data:', error.message);
    mainWindow.webContents.send('log', `Error: ${error.message}`);
    throw new Error(error.message);
  }
});

// Handle analysis of existing data
ipcMain.handle('analyze-existing', async (event, data) => {
  try {
    if (pythonProcess) {
      pythonProcess.kill();
    }

    console.log('Starting analysis of existing data...');
    mainWindow.webContents.send('log', 'Starting analysis of existing data...');

    // Start Python process with the analyze-existing flag
    pythonProcess = spawn('python', ['main.py', '--analyze-existing']);

    // Set up data handling
    setupPythonProcessHandlers(pythonProcess);

    // Notify the renderer that the Python process has started
    mainWindow.webContents.send('python-start');

    // Wait for the analysis choice prompt, unless skipPrompt is true
    if (!data || !data.skipPrompt) {
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Default to RAG-based Q&A
      await sendToPython('2');

      // Notify the UI that the RAG system is being set up
      mainWindow.webContents.send('log', 'Setting up RAG system with existing data...');
      mainWindow.webContents.send('python-output', 'Setting up RAG system with existing data...');

      // Set a flag to indicate that the RAG index is being built
      mainWindow.webContents.send('rag-index-building', true);
    } else {
      // If skipPrompt is true, we're just initializing the Python process for RAG queries
      // We'll automatically select RAG (option 2) when the prompt appears
      console.log('Skipping analysis choice prompt, will auto-select RAG');

      // Flag to track if we've already sent the choice
      let choiceSent = false;

      // Set up a one-time listener for the analysis choice prompt
      const analysisChoiceHandler = (data) => {
        const output = data.toString().trim();

        // Check if this is the analysis choice prompt and we haven't sent the choice yet
        if (!choiceSent &&
            (output.includes('Choose analysis method:') ||
             (output.includes('1. RAG-based Q&A with OpenRouter') && output.includes('2. RAG-based Q&A without LLM')))) {

          console.log('Detected analysis choice prompt, automatically selecting RAG (option 1)');

          // Set the flag to prevent duplicate sends
          choiceSent = true;

          // Small delay before responding
          setTimeout(() => {
            try {
              // Select RAG-only option (2) - faster and doesn't require API key
              pythonProcess.stdin.write('2\n');
              console.log('Automatically selected RAG-only (option 2)');

              // Notify the UI
              mainWindow.webContents.send('log', 'Automatically selected RAG-based Q&A for existing data');
              mainWindow.webContents.send('python-output', 'Setting up RAG system with existing data...');
              mainWindow.webContents.send('rag-index-building', true);

              // Remove the listener after handling
              pythonProcess.stdout.removeListener('data', analysisChoiceHandler);
            } catch (err) {
              console.error('Error sending analysis choice:', err);
            }
          }, 500);
        }
      };

      // Add the listener
      pythonProcess.stdout.on('data', analysisChoiceHandler);
    }

    // Return success
    return { success: true };
  } catch (error) {
    console.error('Error analyzing existing data:', error.message);
    mainWindow.webContents.send('log', `Error: ${error.message}`);
    throw new Error(error.message);
  }
});

// Handle RAG analysis
ipcMain.handle('start-rag', async (event, options = {}) => {
  try {
    // Default to using LLM if not specified
    const useLLM = options.useLLM !== false;

    // Choose RAG option based on whether to use LLM
    // 1 = RAG with LLM, 2 = RAG without LLM (retrieval only)
    const option = useLLM ? '1' : '2';
    console.log(`Starting RAG with option ${option} (${useLLM ? 'with LLM' : 'retrieval only'})`);

    await sendToPython(option);

    // Return success
    return { success: true };
  } catch (error) {
    console.error('Error starting RAG:', error.message);
    throw new Error(error.message);
  }
});

// Flag to track if we've already handled the analysis choice
let analysisChoiceHandled = false;

// Handle analysis choice prompt
function handleAnalysisChoice(output) {
  // Only handle the choice if we haven't already
  if (!analysisChoiceHandled &&
      (output.includes('Choose analysis method:') ||
       (output.includes('1. RAG-based Q&A with OpenRouter') && output.includes('2. RAG-based Q&A without LLM')))) {

    console.log('Detected analysis choice prompt, automatically selecting RAG-only (option 2)');

    // Set the flag to prevent duplicate handling
    analysisChoiceHandled = true;

    // Small delay before responding
    setTimeout(() => {
      try {
        // Select RAG-only option (2) by default - faster and doesn't require API key
        pythonProcess.stdin.write('2\n');
        console.log('Automatically selected RAG-only (option 2)');

        // Notify the UI
        mainWindow.webContents.send('log', 'Automatically selected RAG without LLM (retrieval only)');
        mainWindow.webContents.send('rag-selected');

      } catch (err) {
        console.error('Error sending analysis choice:', err);
      }

      // Reset the flag after a delay to allow for future prompts
      setTimeout(() => {
        analysisChoiceHandled = false;
      }, 5000);

    }, 500);

    return true;
  }

  return false;
}

// Check for existing data
ipcMain.handle('check-existing-data', async () => {
  try {
    // Run the Python script with the --check-data flag
    const pythonCheckProcess = spawn('python', ['main.py', '--check-data']);

    // Collect output
    let output = '';

    // Set up promise to wait for process to complete
    const checkResult = new Promise((resolve, reject) => {
      pythonCheckProcess.stdout.on('data', (data) => {
        output += data.toString();
      });

      pythonCheckProcess.stderr.on('data', (data) => {
        console.error(`Python stderr: ${data}`);
      });

      pythonCheckProcess.on('close', (code) => {
        if (code === 0) {
          resolve(output);
        } else {
          reject(new Error(`Process exited with code ${code}`));
        }
      });
    });

    // Wait for the process to complete
    const result = await checkResult;

    // Parse the output to determine if there's existing data
    const match = result.match(/existing_data:(true|false)(?::(\d+))?/);
    if (match) {
      const hasData = match[1] === 'true';
      const count = match[2] ? parseInt(match[2]) : 0;

      console.log(`Existing data check: ${hasData}, count: ${count}`);

      // Notify the renderer
      mainWindow.webContents.send('existing-data', hasData);

      return hasData;
    }

    return false;
  } catch (error) {
    console.error('Error checking for existing data:', error.message);
    return false;
  }
});

// Handle RAG query
ipcMain.handle('query-rag', async (event, data) => {
  try {
    // IMPORTANT: Check if we have document context
    // If we do, we'll use our standalone function for document analysis
    // This completely bypasses the RAG system
    if (data.documentContext) {
      console.log(`DOCUMENT MODE ACTIVATED: Using document context: ${data.documentContext.fileName} (ID: ${data.documentContext.documentId})`);
      mainWindow.webContents.send('log', `DOCUMENT MODE: Analyzing document: ${data.documentContext.fileName}`);

      try {
        // Get the model to use
        const modelIndex = getModelIndex(data.model);

        // Use our standalone function to handle document analysis
        // This completely bypasses the RAG system
        const command = `analyze_document:${data.documentContext.documentId}:${modelIndex}:${data.query}`;

        // We'll use a simpler approach - create a temporary file with the document content
        // and then analyze it directly with the LLM
        console.log(`Analyzing document ${data.documentContext.documentId} with direct LLM approach`);
        mainWindow.webContents.send('log', `Analyzing document with direct LLM approach (bypassing RAG system)`);

        // First, get the document content
        const { spawn } = require('child_process');
        const getDocProcess = spawn('python', ['main.py', '--get-document', data.documentContext.documentId]);

        let docContent = '';
        let docError = '';

        // Collect output from the process
        getDocProcess.stdout.on('data', (data) => {
          docContent += data.toString();
        });

        // Collect errors from the process
        getDocProcess.stderr.on('data', (data) => {
          docError += data.toString();
          console.error(`Document retrieval error: ${data}`);
        });

        // Wait for the process to complete
        await new Promise((resolve, reject) => {
          getDocProcess.on('close', (code) => {
            if (code === 0) {
              resolve();
            } else {
              reject(new Error(`Document retrieval process exited with code ${code}: ${docError}`));
            }
          });
        });

        // Extract the document content
        let extractedContent = '';
        if (docContent.includes('document_content:')) {
          extractedContent = docContent.replace('document_content:', '');
          console.log(`Successfully extracted document content (${extractedContent.length} characters)`);
        } else {
          console.error('Failed to retrieve document content. Raw output:', docContent);
          throw new Error('Failed to retrieve document content. Check the console for details.');
        }

        console.log(`Retrieved document content (${extractedContent.length} characters)`);
        mainWindow.webContents.send('log', `Retrieved document content (${extractedContent.length} characters)`);

        // Now, analyze the document with the LLM
        // Create a prompt for the LLM
        const prompt = `I need you to analyze and answer a question about the following document:

DOCUMENT: ${data.documentContext.fileName}

USER QUESTION: ${data.query}

DOCUMENT CONTENT:
${extractedContent}

Please provide a comprehensive answer based ONLY on the information in this document.
If the document doesn't contain information to answer the question, clearly state that.
Do not make up information or use your general knowledge to fill in gaps.
Focus specifically on addressing the user's question using only the document content provided.

Start your answer with "Based on the document:" and include relevant quotes or sections from the document to support your answer.
`;

        // Create a system message for the LLM
        const systemMessage = (
          "You are a document analysis assistant. Your task is to analyze documents and answer questions about them. "
          + "Only use information contained in the document provided. "
          + "If the document doesn't contain the information needed to answer the question, clearly state that. "
          + "Do not make up information or use your general knowledge to fill in gaps. "
          + "Be precise, thorough, and accurate in your analysis."
        );

        // Call the LLM directly using OpenRouter
        console.log('Calling LLM directly with document content');
        mainWindow.webContents.send('log', 'Calling LLM directly with document content');

        // Get the API key from settings
        // First check if we have it in the environment
        let apiKey = process.env.OPENROUTER_API_KEY;

        // If not, try to get it from the stored settings
        if (!apiKey) {
          try {
            // Get the stored API key
            apiKey = userSettings.openrouterApiKey;
            if (apiKey) {
              console.log('Retrieved API key from stored settings');
            }
          } catch (err) {
            console.error('Error retrieving API key from stored settings:', err);
          }
        }

        // If we still don't have an API key, throw an error
        if (!apiKey) {
          throw new Error('OpenRouter API key not set. Please set up your API key in settings.');
        }

        console.log(`Using API key (first 4 chars): ${apiKey.substring(0, 4)}...`);
        mainWindow.webContents.send('log', 'Using API key from settings');

        // Call the OpenRouter API directly using dynamic import
        const { default: fetch } = await import('node-fetch');
        const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`,
            'HTTP-Referer': 'http://localhost:3000',
            'X-Title': 'Fungi Document Analysis'
          },
          body: JSON.stringify({
            model: data.model || 'mistralai/mistral-7b-instruct',
            messages: [
              { role: 'system', content: systemMessage },
              { role: 'user', content: prompt }
            ]
          })
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`OpenRouter API error: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const responseData = await response.json();
        const llmResponse = responseData.choices[0].message.content;

        console.log('LLM response received successfully');
        mainWindow.webContents.send('log', 'LLM response received successfully');

        // Add a footer with document information
        const finalResponse = `${llmResponse}\n\nDocument: ${data.documentContext.fileName}`;

        // Format the response for the UI
        const formattedResponse = finalResponse.startsWith('Answer:') || finalResponse.startsWith('Based on the document:')
          ? finalResponse
          : `Answer: ${finalResponse}`;

        // Send the result to the renderer
        // First send the answer as a complete answer
        console.log('Sending document analysis response to UI');
        mainWindow.webContents.send('log', 'Document analysis complete, sending response to UI');
        mainWindow.webContents.send('rag-answer', formattedResponse);

        // Then send the query-complete signal to re-enable the input
        console.log('Sending query-complete signal to re-enable input');
        mainWindow.webContents.send('rag-query-complete');

        // We've sent the command, so we don't need to send the query again
        return { success: true };
      } catch (error) {
        console.error('Error analyzing document:', error);
        mainWindow.webContents.send('log', `Error analyzing document: ${error.message}`);

        // Send an error message to the UI
        const errorMessage = `Error analyzing document: ${error.message}`;
        mainWindow.webContents.send('rag-answer', `Answer: ${errorMessage}`);

        // Re-enable the input
        mainWindow.webContents.send('rag-query-complete');

        return { success: false, error: error.message };
      }
    }

    // If we don't have document context, proceed with normal RAG query
    // Check if Python process is running
    if (!pythonProcess) {
      console.log('Python process not running, starting it for RAG query');
      mainWindow.webContents.send('log', 'Starting Python process for RAG query...');

      // Start Python process with existing data
      pythonProcess = spawn('python', ['main.py', '--analyze-existing']);

      // Set up data handling
      setupPythonProcessHandlers(pythonProcess);

      // Notify the renderer that the Python process has started
      mainWindow.webContents.send('python-start');

      // Wait for the process to initialize
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Set up a flag to track if we've sent the RAG choice
      let ragChoiceSent = false;

      // Set up a one-time listener for the analysis choice prompt
      const choiceHandler = (data) => {
        const output = data.toString().trim();

        // Check if this is the analysis choice prompt and we haven't sent the choice yet
        if (!ragChoiceSent &&
            (output.includes('Choose analysis method:') ||
             (output.includes('1. RAG-based Q&A with OpenRouter') && output.includes('2. RAG-based Q&A without LLM')))) {

          console.log('Detected analysis choice prompt for RAG query, selecting option 1');

          // Set the flag to prevent duplicate sends
          ragChoiceSent = true;

          // Check if we should use LLM or retrieval-only mode
          if (data.useLLM === true) {
            // Use RAG with LLM (option 1)
            pythonProcess.stdin.write('1\n');
            console.log('Using LLM mode for RAG query');
            mainWindow.webContents.send('log', 'Using LLM mode for RAG query');

            // Send a special command to ensure LLM mode is enabled
            setTimeout(() => {
              pythonProcess.stdin.write('use_llm:true\n');
              console.log('Sent explicit LLM mode command');
            }, 500);
          } else {
            // Use RAG without LLM (option 2) - faster and doesn't require API key
            pythonProcess.stdin.write('2\n');
            console.log('Using retrieval-only mode for RAG query');
            mainWindow.webContents.send('log', 'Using retrieval-only mode for RAG query');

            // Send a special command to ensure retrieval-only mode is enabled
            setTimeout(() => {
              pythonProcess.stdin.write('use_llm:false\n');
              console.log('Sent explicit retrieval-only mode command');
            }, 500);
          }

          // Notify the UI
          mainWindow.webContents.send('log', 'Selected RAG-based Q&A for query');

          // Remove the listener after handling
          pythonProcess.stdout.removeListener('data', choiceHandler);
        }
      };

      // Add the listener
      pythonProcess.stdout.on('data', choiceHandler);

      // Wait for the RAG system to initialize
      await new Promise(resolve => setTimeout(resolve, 5000));
    }

    // Store the question for reference
    questionAsked = data.query;
    console.log('Question asked:', questionAsked);

    // Clear any previous answer buffer
    if (typeof answerBuffer !== 'undefined') {
      answerBuffer = '';
      collectingAnswer = false;
    }

    // Check if we have document context
    if (data.documentContext) {
      console.log(`DOCUMENT MODE ACTIVATED: Using document context: ${data.documentContext.fileName} (ID: ${data.documentContext.documentId})`);
      mainWindow.webContents.send('log', `DOCUMENT MODE: Analyzing document: ${data.documentContext.fileName}`);

      // For document analysis, we'll use direct LLM analysis
      // This completely bypasses the RAG system

      try {
        // Get the model to use
        const modelIndex = getModelIndex(data.model);

        // Send a special command to analyze the document directly with LLM
        // This will NOT use the RAG system at all
        mainWindow.webContents.send('log', `Sending document directly to LLM for analysis (bypassing RAG system)`);

        // Use our standalone function to handle document analysis
        // This completely bypasses the RAG system
        const command = `analyze_document:${data.documentContext.documentId}:${modelIndex}:${data.query}`;

        // Use the Python script with the --handle-analyze-document flag
        // This ensures we don't use the RAG system at all
        const { spawn } = require('child_process');
        const pythonProcess = spawn('python', ['main.py', '--handle-analyze-document', command]);

        let result = '';
        let error = '';

        // Collect output from the process
        pythonProcess.stdout.on('data', (data) => {
          result += data.toString();
        });

        // Collect errors from the process
        pythonProcess.stderr.on('data', (data) => {
          error += data.toString();
          console.error(`Document analysis error: ${data}`);
        });

        // Wait for the process to complete
        await new Promise((resolve, reject) => {
          pythonProcess.on('close', (code) => {
            if (code === 0) {
              resolve();
            } else {
              reject(new Error(`Document analysis process exited with code ${code}: ${error}`));
            }
          });
        });

        // Send the result to the renderer
        mainWindow.webContents.send('rag-query-complete', result);

        // We've sent the command, so we don't need to send the query again
        return { success: true };
      } catch (error) {
        console.error('Error analyzing document:', error);
        mainWindow.webContents.send('log', `Error analyzing document: ${error.message}`);
        throw error;
      }
    }

    // Format the query with the query: prefix
    const queryCommand = `query:${data.query}`;

    // Send the formatted query to Python
    await sendToPython(queryCommand);
    console.log(`Sent query to Python: ${queryCommand}`);

    // Select model if needed
    if (data.needsModel) {
      const modelIndex = getModelIndex(data.model);
      await sendToPython(modelIndex.toString());
      console.log(`Sent model selection: ${modelIndex}`);
    }

    // Send the LLM mode command after the query
    if (data.useLLM === true) {
      setTimeout(async () => {
        await sendToPython('use_llm:true');
        console.log('Sent explicit LLM mode command after query');
      }, 1000);
    } else if (data.useLLM === false) {
      setTimeout(async () => {
        await sendToPython('use_llm:false');
        console.log('Sent explicit retrieval-only mode command after query');
      }, 1000);
    }

    // Set a timeout to check if we've received an answer
    setTimeout(() => {
      // If we're still collecting an answer after 300 seconds, send what we have
      if (collectingAnswer && answerBuffer) {
        console.log('Timeout reached, sending collected answer:', answerBuffer);
        mainWindow.webContents.send('rag-answer', answerBuffer);

        // Also send a signal to re-enable the input field
        mainWindow.webContents.send('rag-query-complete');

        collectingAnswer = false;
        answerBuffer = '';
      }
    }, 300000); // 300 seconds (5 minutes) timeout

    // Return success, actual results will be sent via stdout
    return { success: true };
  } catch (error) {
    console.error('Error querying RAG:', error.message);
    throw new Error(error.message);
  }
});

// Handle tools query (direct query without LLM interpretation)
ipcMain.handle('query-tools', async (event, data) => {
  try {
    console.log('Querying tools knowledge base:', data.query);
    mainWindow.webContents.send('log', `Querying tools knowledge base: ${data.query}`);

    // Format the command
    const command = `tools_query:${data.query}:${data.k || 3}`;

    // Use the Python script with the --handle-tools-query flag
    const { spawn } = require('child_process');
    const pythonProcess = spawn('python', ['main.py', '--handle-tools-query', command]);

    let result = '';
    let error = '';

    // Collect output from the process
    pythonProcess.stdout.on('data', (data) => {
      result += data.toString();
    });

    // Collect errors from the process
    pythonProcess.stderr.on('data', (data) => {
      error += data.toString();
      console.error(`Tools query error: ${data}`);
    });

    // Wait for the process to complete
    await new Promise((resolve, reject) => {
      pythonProcess.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`Tools query process exited with code ${code}: ${error}`));
        }
      });
    });

    // Send the result to the renderer
    mainWindow.webContents.send('tools-query-complete', result);

    return { success: true, result };
  } catch (error) {
    console.error('Error querying tools knowledge base:', error);
    mainWindow.webContents.send('log', `Error querying tools knowledge base: ${error.message}`);
    throw error;
  }
});

// Add a new IPC handler to clear the Intelligent Mode error status
ipcMain.handle('clear-intelligent-mode-error', () => {
  console.log('Clearing Intelligent Mode error status');
  mainWindow.webContents.send('intelligent-mode-error', false);
  return { success: true };
});

// Handle intelligent query (with LLM interpretation and tool execution)
ipcMain.handle('intelligent-query', async (event, data) => {
  try {
    console.log('Processing intelligent query:', data.query);
    mainWindow.webContents.send('log', `Processing intelligent query: ${data.query}`);

    // Clear any previous Intelligent Mode error status
    mainWindow.webContents.send('intelligent-mode-error', false);

    // Get the API key first
    const apiKey = process.env.OPENROUTER_API_KEY || userSettings.openrouterApiKey;
    if (!apiKey) {
      throw new Error('API key is required for Intelligent Mode. Please set it in the settings.');
    }

    // STEP 1: First ask the LLM if it can respond directly or needs tools
    console.log('Asking LLM if it can respond directly or needs tools');
    mainWindow.webContents.send('log', 'Determining if tools are needed...');

    // Get relevant conversation history for context
    let initialConversationHistory = '';
    try {
      if (conversationManager) {
        const historyResult = await conversationManager.getRelevantMessages(data.query);
        if (historyResult && historyResult.length > 0) {
          initialConversationHistory = conversationManager.formatMessagesForContext(historyResult);
          console.log(`Retrieved ${historyResult.length} relevant messages from conversation history`);
        }
      }
    } catch (historyError) {
      console.error('Error retrieving conversation history:', historyError);
    }

    // Create a simple decision prompt
    const decisionPrompt = `You are an intelligent assistant. For the following user request, determine if you can respond directly or if you need specialized tools.

User request: "${data.query}"

${initialConversationHistory ? `Recent conversation context:
${initialConversationHistory}` : ''}

RESPOND DIRECTLY if the request is:
- A greeting, casual conversation, or small talk
- A general knowledge question you can answer from your training
- An opinion request or subjective question
- A simple explanation that doesn't require real-time data
- A hypothetical scenario or personal advice
- Questions about the conversation itself

USE TOOLS if the request requires:
- Current/real-time information from the web
- Searching through stored documents or knowledge bases
- Analyzing specific documents
- Complex research on specialized topics
- When the user explicitly asks for more information or research (even on topics discussed before)

Respond with EXACTLY one of these formats:

DIRECT_RESPONSE
[Your complete response to the user]

OR

NEEDS_TOOLS
[Brief explanation of why tools are needed]`;

    const { default: fetchDecision } = await import('node-fetch');
    const decisionResponse = await fetchDecision('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'HTTP-Referer': 'http://localhost:3000',
        'X-Title': 'Fungi Decision Mode'
      },
      body: JSON.stringify({
        model: data.model || userSettings.defaultModel,
        messages: [
          { role: 'user', content: decisionPrompt }
        ]
      })
    });

    if (!decisionResponse.ok) {
      const errorText = await decisionResponse.text();
      throw new Error(`LLM API error: ${decisionResponse.status} ${errorText}`);
    }

    const decisionResult = await decisionResponse.json();
    const decisionContent = decisionResult.choices[0].message.content;

    console.log('LLM decision received:', decisionContent);

    // Parse the decision
    if (decisionContent.startsWith('DIRECT_RESPONSE')) {
      // LLM can respond directly - no tools needed
      const directResponse = decisionContent.replace('DIRECT_RESPONSE', '').trim();

      console.log('LLM determined it can respond directly');
      mainWindow.webContents.send('log', 'Responding directly without tools');

      // Store the conversation
      try {
        if (conversationManager) {
          await conversationManager.addMessage('user', data.query);
          await conversationManager.addMessage('assistant', directResponse);
          console.log('Conversation stored in history');
        }
      } catch (error) {
        console.error('Error storing conversation:', error);
      }

      // Return the direct response
      const finalResult = {
        reasoning: 'Direct conversational response - no tools needed',
        selectedTool: 'Conversation',
        executionPlan: 'Engage in conversation',
        result: {
          type: 'direct_response',
          content: directResponse
        }
      };

      mainWindow.webContents.send('intelligent-query-complete', finalResult);
      return { success: true, result: finalResult };
    }

    // If we get here, the LLM determined tools are needed
    console.log('LLM determined tools are needed, proceeding with full analysis');
    mainWindow.webContents.send('log', 'Tools needed - analyzing available options...');

    // STEP 2: Now do the full RAG analysis and tool selection
    console.log('Querying RAG system for tool selection guidance');
    mainWindow.webContents.send('log', 'Querying tool selection knowledge base...');

    let ragInfo = '';
    let ragRelevanceScore = 0;
    let shouldUseDeepResearch = false;

    try {
      // Query the TOOL SELECTION RAG system
      const ragResult = await runPythonCommand(['rag_query.py', data.query, 'false', '5']);
      console.log('TOOL SELECTION RAG system response received');

      // Check if the TOOL SELECTION RAG system found relevant information
      if (ragResult.includes('No relevant information found') ||
          ragResult.includes('RELEVANCE_SCORE: 0')) {
        console.log('TOOL SELECTION RAG system found no relevant information');
        ragInfo = "The tool selection knowledge base contains no relevant information about this query.";
        ragRelevanceScore = 0;
        shouldUseDeepResearch = true;
      } else {
        // Extract relevance score if present
        const relevanceMatch = ragResult.match(/RELEVANCE_SCORE:\s*(\d+(\.\d+)?)\s*\/\s*10/i);
        if (relevanceMatch && relevanceMatch[1]) {
          ragRelevanceScore = parseFloat(relevanceMatch[1]);
          console.log(`TOOL SELECTION RAG system relevance score: ${ragRelevanceScore}/10`);
          mainWindow.webContents.send('log', `Tool selection relevance score: ${ragRelevanceScore}/10`);

          // Remove the relevance score from the response
          ragInfo = ragResult.replace(/RELEVANCE_SCORE:\s*\d+(\.\d+)?\s*\/\s*10/i, '').trim();

          // Check if the relevance score is below threshold
          if (ragRelevanceScore < 5) {
            console.log(`TOOL SELECTION RAG relevance score ${ragRelevanceScore}/10 is below threshold (5). Will recommend Deep Research.`);
            mainWindow.webContents.send('log', `Low tool selection relevance score (${ragRelevanceScore}/10). Will recommend Deep Research.`);
            shouldUseDeepResearch = true;

            // Add a strong warning about the low relevance score
            ragInfo = `CRITICAL WARNING: The TOOL SELECTION relevance score is low (${ragRelevanceScore}/10), indicating that the tool selection knowledge base does not contain sufficiently relevant information for this query. Deep Research MUST be used instead of RAG Query.\n\n${ragInfo}`;
          }
        } else {
          // If no explicit score, assume medium relevance
          ragRelevanceScore = 5;
          ragInfo = ragResult;
        }
      }
    } catch (ragError) {
      console.error('Error querying TOOL SELECTION RAG system:', ragError);
      mainWindow.webContents.send('log', `Error querying tool selection knowledge base: ${ragError.message}`);
      ragInfo = "Error querying the tool selection knowledge base.";
      ragRelevanceScore = 0;
      shouldUseDeepResearch = true;
    }

    // Get relevant tools from the knowledge base
    const toolsCommand = `tools_query:${data.query}:${data.k || 3}`;
    const toolsResult = await runPythonCommand(['main.py', '--handle-tools-query', toolsCommand]);

    // Extract the tools information (remove the "Answer: " prefix)
    let toolsInfo = toolsResult;
    if (toolsInfo.startsWith('Answer:')) {
      toolsInfo = toolsInfo.substring(7).trim();
    }

    // Create a system message that instructs the LLM how to interpret tools
    const systemMessage = `You are an intelligent assistant that can use tools to help users accomplish tasks.
Based on the user's request, the available tools, and information from the knowledge base, you should:
1. Determine which tool or procedure is most appropriate for the task
2. Explain your reasoning for selecting that tool
3. Provide step-by-step instructions on how to use the tool
4. Format your response in a way that can be parsed to execute the tool

YOUR RESPONSE MUST STRICTLY FOLLOW THIS FORMAT:
REASONING: [Your reasoning for selecting the tool]
SELECTED_TOOL: [Name of the selected tool - must be exactly one of: Conversation, Web Search, Deep Research, Document Analysis, RAG Query, File Import, Pinecone Cloud Storage, or none]
EXECUTION_PLAN: [Step-by-step plan for using the tool]
PARAMETERS: [JSON object with parameters needed to execute the tool]

Available parameters for tools:
- Conversation: No parameters needed
- Web Search/Deep Research: query, resultsCount
- Document Analysis: query, documentId
- RAG Query: query, useLLM
- File Import: none (UI action required)
- Pinecone Cloud Storage: apiKey, environment, indexName, usePinecone

If the user's request is a general question, greeting, opinion request, or casual conversation, use:
REASONING: [Your reasoning]
SELECTED_TOOL: Conversation
EXECUTION_PLAN: Engage in conversation
RESPONSE: [Your direct response to the user's question]

CRITICAL INSTRUCTIONS:
1. You MUST include the SELECTED_TOOL section in your response, even if you select "none" or "Conversation".
2. Use your best judgment to determine which tool is most appropriate for each query.
3. Consider whether the query requires real-time information, database lookups, document analysis, or is just casual conversation.
4. The tool name must be exactly as specified above - do not add quotes, prefixes, or suffixes.

Tool Guidelines:
- Conversation: Use for casual chat, greetings, opinions, general knowledge questions, and any request that doesn't need specialized tools.
- Deep Research: Use for queries that require up-to-date information from the web, such as current events, real-time data, or comprehensive research on a topic.
- RAG Query: Use for queries that might be answered using information already in the knowledge base.
- Document Analysis: Use when the user wants to analyze a specific document they've provided.
- File Import/Pinecone Cloud Storage: Use when the user wants to manage their data storage.

CONVERSATION MODE CRITERIA:
- General knowledge questions that don't require specialized research
- Casual conversation, greetings, or small talk
- Requests for opinions or subjective information
- Simple explanations that don't require real-time data
- Questions about hypothetical scenarios
- Personal advice or guidance that doesn't require specialized knowledge

⚠️ HIGHEST PRIORITY INSTRUCTION ⚠️
When deciding which tool to use, you MUST follow these rules based on the TOOL SELECTION relevance score:
1. If the TOOL SELECTION knowledge base contains relevant information with high relevance (score > 7/10), use RAG Query.
2. If the TOOL SELECTION knowledge base has no relevant information or low relevance (score < 5/10), you MUST use Deep Research instead of RAG Query.
3. For TOOL SELECTION relevance scores between 5-7, use your judgment based on the quality of the information.
4. If the query is clearly conversational in nature, use Conversation regardless of the relevance score.

The TOOL SELECTION relevance score is the MOST IMPORTANT factor in your decision when choosing between RAG Query and Deep Research. If the score is below 5/10, you MUST select Deep Research even if the knowledge base contains some information about the topic. This is a strict requirement that overrides all other considerations.

IMPORTANT: There are two separate RAG systems in this application:
1. TOOL SELECTION RAG - Used to help you decide which tool to use (score provided to you now)
2. CONTENT RAG - Used to retrieve actual content if RAG Query is selected (score calculated later)

Example for a query with low TOOL SELECTION relevance score (below 5/10):
REASONING: The user's query received a TOOL SELECTION relevance score of 3.5/10, indicating the tool selection knowledge base does not contain sufficiently relevant information. Therefore, I must use Deep Research to find better information from the web.
SELECTED_TOOL: Deep Research
EXECUTION_PLAN:
1. Search for relevant information on the web
2. Extract key details from reliable sources
3. Present a comprehensive answer to the user
PARAMETERS:
{
  "query": "user's query with relevant search terms",
  "resultsCount": 5
}

Example for a query with high TOOL SELECTION relevance score (above 7/10):
REASONING: The user's query received a TOOL SELECTION relevance score of 8.2/10, indicating the tool selection knowledge base contains highly relevant information. Therefore, I should use RAG Query to leverage the CONTENT RAG system.
SELECTED_TOOL: RAG Query
EXECUTION_PLAN:
1. Search the content knowledge base for relevant information
2. Retrieve and synthesize the information
3. Present a comprehensive answer to the user
PARAMETERS:
{
  "query": "user's query",
  "useLLM": true
}

Example for a conversational query:
REASONING: The user is asking a general question that is conversational in nature and doesn't require specialized tools or research.
SELECTED_TOOL: Conversation
EXECUTION_PLAN: Engage in conversation
RESPONSE: [Your direct response to the user's question]

IMPORTANT NOTE: Even if you select RAG Query based on a high TOOL SELECTION relevance score, the CONTENT RAG system will still calculate its own relevance score. If that score is below 5/10, the system will automatically switch to Deep Research.`;

    // Get relevant conversation history for tool selection
    let toolSelectionConversationHistory = '';
    try {
      if (conversationManager) {
        const historyResult = await conversationManager.getRelevantMessages(data.query);
        if (historyResult && historyResult.length > 0) {
          toolSelectionConversationHistory = conversationManager.formatMessagesForContext(historyResult);
          console.log(`Retrieved ${historyResult.length} relevant messages from conversation history`);
          mainWindow.webContents.send('log', `Retrieved ${historyResult.length} relevant messages from conversation history`);
        } else {
          console.log('No relevant conversation history found');
        }
      }
    } catch (historyError) {
      console.error('Error retrieving conversation history:', historyError);
    }

    // Create a user message that includes the RAG information, tools information, conversation history, and the user's query
    const userMessage = `I need help with the following request: "${data.query}"

TOOL SELECTION RELEVANCE SCORE: ${ragRelevanceScore}/10
${shouldUseDeepResearch ? '⚠️ CRITICAL: THIS TOOL SELECTION SCORE IS BELOW 5/10. YOU MUST USE DEEP RESEARCH INSTEAD OF RAG QUERY ⚠️' : ''}

INFORMATION FROM TOOL SELECTION KNOWLEDGE BASE:
${ragInfo}

AVAILABLE TOOLS AND PROCEDURES:
${toolsInfo}

${toolSelectionConversationHistory ? `RELEVANT CONVERSATION HISTORY:
${toolSelectionConversationHistory}` : ''}

Based on this information, which tool should I use and how should I use it?

CRITICAL INSTRUCTION: You MUST follow these rules about relevance scores:
- If the TOOL SELECTION relevance score is below 5/10, you MUST use Deep Research instead of RAG Query
- If the TOOL SELECTION relevance score is above 7/10, use RAG Query
- For scores between 5-7, use your judgment based on the quality of information

The current TOOL SELECTION relevance score is ${ragRelevanceScore}/10.
${shouldUseDeepResearch ? 'Since the TOOL SELECTION relevance score is below 5/10, you MUST select Deep Research as the tool.' : ''}

IMPORTANT: This TOOL SELECTION relevance score is different from the CONTENT relevance score that will be calculated later if you choose RAG Query. The TOOL SELECTION score helps you decide which tool to use, while the CONTENT score determines if the RAG Query results are relevant enough to show to the user.`;

    // Call the LLM API
    console.log('Calling LLM for tool interpretation');
    mainWindow.webContents.send('log', 'Calling LLM to interpret tools and user request');

    const { default: fetchToolSelection } = await import('node-fetch');
    const response = await fetchToolSelection('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'HTTP-Referer': 'http://localhost:3000',
        'X-Title': 'Fungi Intelligent Mode'
      },
      body: JSON.stringify({
        model: data.model || userSettings.defaultModel,
        messages: [
          { role: 'system', content: systemMessage },
          { role: 'user', content: userMessage }
        ]
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`LLM API error: ${response.status} ${errorText}`);
    }

    const llmResponse = await response.json();
    const llmContent = llmResponse.choices[0].message.content;

    console.log('=== INTELLIGENT MODE LLM RESPONSE DEBUG ===');
    console.log('Raw LLM Response Object:', JSON.stringify(llmResponse, null, 2));
    console.log('LLM Content Length:', llmContent ? llmContent.length : 'null/undefined');
    console.log('LLM Content (first 500 chars):', llmContent ? llmContent.substring(0, 500) : 'null/undefined');
    console.log('LLM Content (full):', llmContent);
    console.log('=== END INTELLIGENT MODE LLM RESPONSE DEBUG ===');

    // Step 3: Parse the LLM response to extract tool selection and parameters
    console.log('=== PARSING LLM RESPONSE ===');
    console.log('About to parse LLM content...');
    const toolSelection = parseLLMResponse(llmContent);
    console.log('Parsed tool selection:', JSON.stringify(toolSelection, null, 2));
    console.log('=== END PARSING LLM RESPONSE ===');

    // Step 4: If no tool was explicitly selected but the reasoning suggests one, detect the intent
    if (toolSelection.selectedTool === 'none' && !toolSelection.response && toolSelection.reasoning) {
      // Try to detect the intended tool from the reasoning
      const detectedTool = detectToolFromReasoning(toolSelection.reasoning, data.query);

      if (detectedTool) {
        console.log(`No tool explicitly selected, but detected intent for: ${detectedTool.tool}`);
        mainWindow.webContents.send('log', `Detected intent for tool: ${detectedTool.tool}`);

        // Confirm the detected tool with the LLM
        const confirmationResult = await confirmToolWithLLM(
          apiKey,
          data.model || userSettings.defaultModel,
          data.query,
          detectedTool.tool,
          detectedTool.reason
        );

        // Determine which tool to use based on confidence score and suggestions
        let toolToUse = null;
        let reasonToUse = '';
        let sourceName = '';

        // Check if we should use the LLM's judgment or consider RAG suggestions
        const useRagSuggestions = confirmationResult.useRagSuggestions;

        if (confirmationResult.confirmed && !useRagSuggestions) {
          // High confidence confirmation - use the detected tool
          console.log(`LLM confirmed the detected tool with confidence ${confirmationResult.confidenceScore}/10`);
          toolToUse = detectedTool.tool;
          reasonToUse = detectedTool.reason;
          sourceName = 'LLM confirmation (high confidence)';
        } else if (confirmationResult.suggestedTool && !useRagSuggestions) {
          // High confidence alternative suggestion - use the LLM's suggested tool
          console.log(`LLM suggested alternative tool: ${confirmationResult.suggestedTool} with confidence ${confirmationResult.confidenceScore}/10`);
          toolToUse = confirmationResult.suggestedTool;
          reasonToUse = `The LLM suggested using ${confirmationResult.suggestedTool} because: ${confirmationResult.explanation}`;
          sourceName = 'LLM suggestion (high confidence)';
        } else if (useRagSuggestions && confirmationResult.ragSuggestions && confirmationResult.ragSuggestions.length > 0) {
          // Low confidence - use RAG suggestion if available
          const ragSuggestion = confirmationResult.ragSuggestions[0];
          console.log(`Using RAG suggestion: ${ragSuggestion.tool} (LLM confidence was low: ${confirmationResult.confidenceScore}/10)`);
          toolToUse = ragSuggestion.tool;
          reasonToUse = `Based on similar past queries, the system suggests using ${ragSuggestion.tool}. Context: ${ragSuggestion.context}`;
          sourceName = 'RAG system suggestion (low LLM confidence)';
        } else if (confirmationResult.confirmed) {
          // Low confidence confirmation but no RAG suggestions - still use the detected tool
          console.log(`LLM confirmed the detected tool with low confidence ${confirmationResult.confidenceScore}/10, but no RAG suggestions available`);
          toolToUse = detectedTool.tool;
          reasonToUse = detectedTool.reason;
          sourceName = 'LLM confirmation (low confidence)';
        } else if (confirmationResult.suggestedTool) {
          // Low confidence alternative suggestion but no RAG suggestions - use the LLM's suggested tool
          console.log(`LLM suggested alternative tool: ${confirmationResult.suggestedTool} with low confidence ${confirmationResult.confidenceScore}/10, but no RAG suggestions available`);
          toolToUse = confirmationResult.suggestedTool;
          reasonToUse = `The LLM suggested using ${confirmationResult.suggestedTool} because: ${confirmationResult.explanation}`;
          sourceName = 'LLM suggestion (low confidence)';
        } else {
          console.log('No tool suggestions available');
          // Keep the original tool selection (none)
        }

        // Log the source of the tool suggestion
        if (toolToUse) {
          console.log(`Tool suggestion source: ${sourceName}`);
          mainWindow.webContents.send('log', `Tool suggestion source: ${sourceName}`);
        }

        // If we have a tool to use (either confirmed or suggested), ask the user
        if (toolToUse) {
          // Ask the user for confirmation
          const userConfirmation = await askUserForConfirmation(toolToUse, reasonToUse);

          if (userConfirmation) {
            console.log(`User confirmed the tool: ${toolToUse}`);

            // Update the tool selection with the selected tool
            toolSelection.selectedTool = toolToUse;
            toolSelection.executionPlan = `Auto-detected plan: Execute ${toolToUse} to find information about "${data.query}"`;
            toolSelection.parameters = { query: data.query, resultsCount: 5 };
          } else {
            console.log('User rejected the tool');
            // Keep the original tool selection (none)
          }
        }
      }
    }

    // Step 5: Execute the selected tool or return the direct response
    let executionResult = null;

    if (toolSelection.selectedTool === 'none') {
      // No tool selected, just return the LLM's direct response
      executionResult = {
        type: 'direct_response',
        content: toolSelection.response || 'No direct response provided by the LLM.'
      };
    } else {
      // Add the relevancy score to the data object
      const enhancedData = {
        ...data,
        ragRelevanceScore: ragRelevanceScore,
        shouldUseDeepResearch: shouldUseDeepResearch
      };

      // Execute the selected tool with the enhanced data
      console.log('=== EXECUTING SELECTED TOOL ===');
      console.log('Tool selection for execution:', JSON.stringify(toolSelection, null, 2));
      console.log('Enhanced data for execution:', JSON.stringify(enhancedData, null, 2));
      executionResult = await executeSelectedTool(toolSelection, enhancedData);
      console.log('Execution result:', JSON.stringify(executionResult, null, 2));
      console.log('=== END EXECUTING SELECTED TOOL ===');
    }

    // Step 6: Store the user's message in the conversation manager
    try {
      if (conversationManager) {
        await conversationManager.addMessage('user', data.query);
        console.log('User message stored in conversation history');
      }
    } catch (error) {
      console.error('Error storing user message in conversation history:', error);
    }

    // Step 7: Store the assistant's response in the conversation manager
    console.log('=== PROCESSING ASSISTANT RESPONSE ===');
    console.log('Execution result type:', executionResult.type);
    console.log('Execution result content (first 200 chars):', executionResult.content ? executionResult.content.substring(0, 200) : 'null/undefined');

    let assistantResponse = '';
    if (executionResult.type === 'direct_response') {
      assistantResponse = executionResult.content;
      console.log('Using direct response content');
    } else if (executionResult.type === 'rag_query' && executionResult.content) {
      assistantResponse = executionResult.content;
      console.log('Using RAG query content');
    } else if (executionResult.type === 'deep_research' && executionResult.content) {
      assistantResponse = executionResult.content;
      console.log('Using deep research content');
    } else if (executionResult.content) {
      assistantResponse = executionResult.content;
      console.log('Using generic content');
    } else {
      assistantResponse = `I used the ${toolSelection.selectedTool} tool to help answer your question.`;
      console.log('No content available, using fallback message');
    }

    console.log('Final assistant response (first 200 chars):', assistantResponse ? assistantResponse.substring(0, 200) : 'null/undefined');
    console.log('=== END PROCESSING ASSISTANT RESPONSE ===');

    try {
      if (conversationManager && assistantResponse) {
        await conversationManager.addMessage('assistant', assistantResponse);
        console.log('Assistant response stored in conversation history');
      }
    } catch (error) {
      console.error('Error storing assistant response in conversation history:', error);
    }

    // Step 8: Send the result to the renderer
    const finalResult = {
      reasoning: toolSelection.reasoning,
      selectedTool: toolSelection.selectedTool,
      executionPlan: toolSelection.executionPlan,
      result: executionResult
    };

    console.log('=== SENDING FINAL RESULT TO UI ===');
    console.log('Final result object:', JSON.stringify(finalResult, null, 2));
    console.log('Final result.result.content (first 200 chars):', finalResult.result && finalResult.result.content ? finalResult.result.content.substring(0, 200) : 'null/undefined');
    console.log('=== END SENDING FINAL RESULT TO UI ===');

    // Check if we need to delay sending the response to the UI
    // This is only needed for RAG Query results where we want to ensure
    // the content relevancy score has been evaluated
    if (toolSelection.selectedTool.toLowerCase().includes('rag query') &&
        executionResult && executionResult.type === 'rag_query') {
      console.log('Delaying response to UI to ensure content relevancy score has been evaluated');

      // We'll add a small delay to ensure any knowledge base updates have been processed
      setTimeout(() => {
        console.log('Sending delayed response to UI');
        mainWindow.webContents.send('intelligent-query-complete', finalResult);
      }, 1000); // 1 second delay
    } else {
      // For all other tools, send the response immediately
      console.log('Sending immediate response to UI');
      mainWindow.webContents.send('intelligent-query-complete', finalResult);
    }

    return { success: true, result: finalResult };
  } catch (error) {
    console.error('Error processing intelligent query:', error);
    mainWindow.webContents.send('log', `Error in intelligent query: ${error.message}`);
    mainWindow.webContents.send('intelligent-query-error', { error: error.message });
    throw error;
  }
});

// Helper function to detect the intended tool from the LLM's reasoning
function detectToolFromReasoning(reasoning, query) {
  // Convert to lowercase for case-insensitive matching
  const lowerReasoning = reasoning.toLowerCase();
  const lowerQuery = query.toLowerCase();

  // Check for Conversation intent first
  if (
    lowerReasoning.includes('general question') ||
    lowerReasoning.includes('casual conversation') ||
    lowerReasoning.includes('greeting') ||
    lowerReasoning.includes('opinion') ||
    lowerReasoning.includes('small talk') ||
    lowerReasoning.includes('chat') ||
    lowerReasoning.includes('conversational') ||
    lowerReasoning.includes('subjective information') ||
    lowerReasoning.includes('hypothetical scenario') ||
    lowerReasoning.includes('personal advice')
  ) {
    return {
      tool: 'Conversation',
      reason: 'The reasoning suggests this is a conversational query that doesn\'t require specialized tools.'
    };
  }

  // Check if the reasoning explicitly mentions web search or deep research
  if (
    lowerReasoning.includes('web search') ||
    lowerReasoning.includes('search the web') ||
    lowerReasoning.includes('internet search') ||
    lowerReasoning.includes('online search') ||
    lowerReasoning.includes('deep research') ||
    lowerReasoning.includes('up-to-date information') ||
    lowerReasoning.includes('real-time information') ||
    lowerReasoning.includes('current information')
  ) {
    return {
      tool: 'Deep Research',
      reason: 'The reasoning suggests this query requires up-to-date information from the web.'
    };
  }

  // Check for RAG Query intent
  if (
    lowerReasoning.includes('knowledge base') ||
    lowerReasoning.includes('database') ||
    lowerReasoning.includes('stored information') ||
    lowerReasoning.includes('rag') ||
    lowerReasoning.includes('vector database') ||
    lowerReasoning.includes('existing data')
  ) {
    return {
      tool: 'RAG Query',
      reason: 'The reasoning suggests this query might be answered using information in our knowledge base.'
    };
  }

  // Check for Document Analysis intent
  if (
    lowerReasoning.includes('document analysis') ||
    lowerReasoning.includes('analyze document') ||
    lowerReasoning.includes('document content') ||
    lowerReasoning.includes('attached document')
  ) {
    return {
      tool: 'Document Analysis',
      reason: 'The reasoning suggests this query requires analyzing a specific document.'
    };
  }

  // If no specific intent is detected, return null
  return null;
}

// Helper function to confirm the detected tool with the LLM
async function confirmToolWithLLM(apiKey, model, query, detectedTool, reason) {
  try {
    console.log(`Confirming detected tool "${detectedTool}" with LLM`);

    // First, check if we have any relevant suggestions in the RAG system
    let ragSuggestions = [];
    try {
      // Query the RAG system for similar tool suggestions
      const ragQuery = `tool suggestion for: ${query} detected tool: ${detectedTool}`;
      console.log(`Querying RAG system for tool suggestions: "${ragQuery}"`);

      // Use the Python script to query the RAG system
      const ragResult = await runPythonCommand(['rag_query.py', ragQuery, 'false', '3']);

      if (ragResult && ragResult.trim()) {
        // Parse the RAG results to extract tool suggestions
        const lines = ragResult.split('\n');
        for (const line of lines) {
          if (line.includes('SUGGESTED_TOOL:')) {
            const toolMatch = line.match(/SUGGESTED_TOOL:\s*([A-Za-z\s]+)/);
            if (toolMatch && toolMatch[1]) {
              ragSuggestions.push({
                tool: toolMatch[1].trim(),
                context: line
              });
            }
          }
        }

        if (ragSuggestions.length > 0) {
          console.log(`Found ${ragSuggestions.length} tool suggestions in RAG system:`, ragSuggestions);
        }
      }
    } catch (error) {
      console.error('Error querying RAG system for tool suggestions:', error);
      // Create the rag_query.py file if it doesn't exist
      try {
        const fs = require('fs');
        const path = require('path');
        const ragQueryPath = path.join(__dirname, 'rag_query.py');

        if (!fs.existsSync(ragQueryPath)) {
          console.log('rag_query.py not found, creating it...');
          // We'll just log this for now - the file should be created separately
          mainWindow.webContents.send('log', 'Note: rag_query.py script not found. Please restart the application after this session.');
        }
      } catch (fsError) {
        console.error('Error checking for rag_query.py:', fsError);
      }

      // Continue with LLM confirmation even if RAG query fails
    }

    // Create a system message that includes RAG suggestions if available
    let systemMessage = `You are an intelligent assistant that helps confirm tool selections.
You need to determine if the detected tool is appropriate for the user's query and provide a confidence score.

Here are the available tools and their purposes:
- Conversation: For casual chat, greetings, opinions, general knowledge questions, and any request that doesn't need specialized tools.
- Deep Research: For queries that require up-to-date information from the web, such as current events, real-time data, comprehensive research on a topic, or complex evolving topics like Artificial General Intelligence.
- RAG Query: For queries that might be answered using information already in the knowledge base.
- Document Analysis: For when the user wants to analyze a specific document they've provided.
- File Import/Pinecone Cloud Storage: For when the user wants to manage their data storage.

CONVERSATION MODE CRITERIA:
- General knowledge questions that don't require specialized research
- Casual conversation, greetings, or small talk
- Requests for opinions or subjective information
- Simple explanations that don't require real-time data
- Questions about hypothetical scenarios
- Personal advice or guidance that doesn't require specialized knowledge

COMPLEX TOPICS REQUIRING DEEP RESEARCH:
1. Artificial General Intelligence (AGI):
   - Criteria and definitions are evolving
   - Research is ongoing and perspectives vary widely
   - Latest developments may not be in knowledge base
   - Multiple expert viewpoints should be considered

2. Cutting-edge technology:
   - Quantum computing advances
   - New AI architectures and capabilities
   - Emerging biotechnology
   - Space exploration developments

3. Current events and policies:
   - Recent political developments
   - New legislation or regulations
   - Economic trends and market movements
   - Social movements and cultural shifts

IMPORTANT CONSIDERATIONS:
1. If the query is conversational in nature (greeting, opinion, general chat), Conversation mode is most appropriate.
2. If the query is about a topic that might not be in our knowledge base (like cooking recipes, specific how-to guides, or current events), consider whether Deep Research is appropriate.
3. If the query is about a general topic that might be in our knowledge base, consider whether RAG Query is appropriate.
4. If the detected tool is Deep Research but the information might not be available or reliable from web searches, consider suggesting RAG Query instead.
5. If the detected tool is RAG Query but the information is likely not in our knowledge base, consider suggesting Deep Research instead.

CONFIDENCE SCORING:
- Provide a confidence score from 1-10 on how confident you are in your tool selection
- Scores of 1-4 indicate low confidence, 5-7 medium confidence, and 8-10 high confidence
- If your confidence score is 5 or higher, your tool selection will be used
- If your confidence score is below 5, RAG system suggestions may be used instead
- Base your confidence on the clarity of the query, match between query and tool capabilities, and likelihood of information being in the knowledge base

RESPONSE FORMAT:
If you think the detected tool is appropriate, respond with:
"yes (confidence: X/10)" followed by a brief explanation.

If you think the detected tool is NOT appropriate, respond with:
"no (confidence: X/10)" followed by a brief explanation AND suggest the correct tool in this format: "SUGGESTED_TOOL: [tool name]"

IMPORTANT: RAG system suggestions are meant to assist you when you're uncertain, not override your confident decisions. Maintain your decision-making authority, especially for high-confidence tool selections.`;

    // Add RAG suggestions if available
    if (ragSuggestions.length > 0) {
      systemMessage += `\n\nHere are some suggestions from previous similar queries (these are just suggestions, use your own judgment):`;
      for (const suggestion of ragSuggestions) {
        systemMessage += `\n- ${suggestion.context}`;
      }
    }

    const userMessage = `User query: "${query}"
Detected tool: ${detectedTool}
Reason for detection: ${reason}

Is this the appropriate tool for this query? If not, please suggest the correct tool.`;

    const { default: fetch } = await import('node-fetch');
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'HTTP-Referer': 'http://localhost:3000',
        'X-Title': 'Fungi Tool Confirmation'
      },
      body: JSON.stringify({
        model: model,
        messages: [
          { role: 'system', content: systemMessage },
          { role: 'user', content: userMessage }
        ]
      })
    });

    if (!response.ok) {
      console.error(`LLM API error: ${response.status}`);
      return { confirmed: false };
    }

    const llmResponse = await response.json();
    const llmContent = llmResponse.choices[0].message.content;

    console.log(`LLM confirmation response: "${llmContent}"`);

    // Extract confidence score
    let confidenceScore = 5; // Default to medium confidence
    const confidenceMatch = llmContent.match(/confidence:\s*(\d+)\/10/i);
    if (confidenceMatch && confidenceMatch[1]) {
      confidenceScore = parseInt(confidenceMatch[1], 10);
      console.log(`Extracted confidence score: ${confidenceScore}/10`);
    }

    // Check if the response starts with "yes"
    const confirmed = llmContent.toLowerCase().startsWith('yes');

    // Extract suggested tool if present
    let suggestedTool = null;
    const toolMatch = llmContent.match(/SUGGESTED_TOOL:\s*([A-Za-z\s]+)/);
    if (toolMatch && toolMatch[1]) {
      suggestedTool = toolMatch[1].trim();
      console.log(`LLM suggested alternative tool: "${suggestedTool}" with confidence ${confidenceScore}/10`);

      // Store this suggestion in the RAG system for future reference
      try {
        // Create a document with the tool suggestion
        const suggestionDoc = `Query: "${query}"\nDetected tool: ${detectedTool}\nReason: ${reason}\nLLM response: ${llmContent}\nSUGGESTED_TOOL: ${suggestedTool}\nCONFIDENCE_SCORE: ${confidenceScore}/10`;

        // Save to a temporary file
        const fs = require('fs');
        const os = require('os');
        const path = require('path');
        const tempFile = path.join(os.tmpdir(), 'tool_suggestion.txt');
        fs.writeFileSync(tempFile, suggestionDoc);

        // Add to the RAG system
        console.log('Storing tool suggestion in RAG system');
        const addResult = await runPythonCommand(['add_document.py', tempFile, 'tool_suggestion', 'Tool suggestion for query']);
        console.log('Tool suggestion stored in RAG system:', addResult);

        // Clean up
        fs.unlinkSync(tempFile);
      } catch (error) {
        console.error('Error storing tool suggestion in RAG system:', error);
        // Continue even if storing fails
      }
    }

    // Determine whether to use LLM's judgment or RAG suggestions based on confidence score
    const useRagSuggestions = confidenceScore < 5;

    if (useRagSuggestions) {
      console.log(`Confidence score ${confidenceScore}/10 is below threshold (5). Will consider RAG suggestions.`);
    } else {
      console.log(`Confidence score ${confidenceScore}/10 is at or above threshold (5). Will use LLM's judgment.`);
    }

    return {
      confirmed,
      explanation: llmContent.replace(/^(yes|no)\s*(\(confidence:[^)]+\))?\s*/, '').trim(),
      suggestedTool,
      confidenceScore,
      useRagSuggestions,
      ragSuggestions: ragSuggestions.length > 0 ? ragSuggestions : null
    };
  } catch (error) {
    console.error('Error confirming tool with LLM:', error);
    return { confirmed: false };
  }
}

// Helper function to ask the user for confirmation
async function askUserForConfirmation(detectedTool, reason) {
  return new Promise((resolve) => {
    // Send a message to the renderer to show the confirmation dialog
    mainWindow.webContents.send('show-tool-confirmation', {
      tool: detectedTool,
      reason: reason
    });

    // Handle the user's response
    ipcMain.once('tool-confirmation-response', (event, confirmed) => {
      resolve(confirmed);
    });

    // No timeout - wait for user response indefinitely
  });
}

// Helper function to run a Python command and return the result
async function runPythonCommand(args) {
  return new Promise((resolve, reject) => {
    const pythonProcess = spawn('python', args);

    let result = '';
    let error = '';

    pythonProcess.stdout.on('data', (data) => {
      result += data.toString();
    });

    pythonProcess.stderr.on('data', (data) => {
      error += data.toString();
      console.error(`Python error: ${data}`);
    });

    pythonProcess.on('close', (code) => {
      if (code === 0) {
        resolve(result);
      } else {
        reject(new Error(`Python process exited with code ${code}: ${error}`));
      }
    });
  });
}

// Helper function to parse the LLM response
function parseLLMResponse(llmContent) {
  const result = {
    reasoning: '',
    selectedTool: 'none',
    executionPlan: '',
    parameters: {},
    response: ''
  };

  console.log('Parsing LLM response:', llmContent);

  // Extract reasoning
  const reasoningMatch = llmContent.match(/REASONING:\s*([\s\S]*?)(?=SELECTED_TOOL:|$)/i);
  if (reasoningMatch && reasoningMatch[1]) {
    result.reasoning = reasoningMatch[1].trim();
  }

  // Extract selected tool
  const toolMatch = llmContent.match(/SELECTED_TOOL:\s*([\s\S]*?)(?=EXECUTION_PLAN:|PARAMETERS:|RESPONSE:|$)/i);
  if (toolMatch && toolMatch[1]) {
    const toolName = toolMatch[1].trim();

    // Clean up the tool name - remove any quotes or extra characters
    const cleanToolName = toolName.replace(/["'`]/g, '').trim();

    console.log(`Extracted tool name: "${cleanToolName}"`);
    result.selectedTool = cleanToolName;
  } else {
    // If no tool is explicitly selected but the reasoning suggests one
    if (result.reasoning) {
      console.log('No tool explicitly selected, will try to detect from reasoning');
    }
  }

  // Extract execution plan
  const planMatch = llmContent.match(/EXECUTION_PLAN:\s*([\s\S]*?)(?=PARAMETERS:|RESPONSE:|$)/i);
  if (planMatch && planMatch[1]) {
    result.executionPlan = planMatch[1].trim();
  } else if (result.selectedTool !== 'none' && result.selectedTool !== '') {
    // Create a default execution plan if none was provided
    result.executionPlan = `Execute ${result.selectedTool} to find information about the query`;
  }

  // Extract parameters
  const paramsMatch = llmContent.match(/PARAMETERS:\s*([\s\S]*?)(?=RESPONSE:|$)/i);
  if (paramsMatch && paramsMatch[1]) {
    try {
      // Try to parse as JSON
      const paramsText = paramsMatch[1].trim();
      // Find the JSON object within the text (it might have additional text around it)
      const jsonMatch = paramsText.match(/(\{[\s\S]*\})/);
      if (jsonMatch && jsonMatch[1]) {
        result.parameters = JSON.parse(jsonMatch[1]);
      }
    } catch (error) {
      console.error('Error parsing parameters JSON:', error);

      // If we couldn't parse the parameters but we have a selected tool, create default parameters
      if (result.selectedTool !== 'none' && result.selectedTool !== '') {
        if (result.selectedTool === 'Deep Research' || result.selectedTool === 'Web Search') {
          // Extract query from the LLM response if possible
          const queryMatch = llmContent.match(/query: "([^"]+)"/i) ||
                            llmContent.match(/query:\s*([^,\n]+)/i);

          if (queryMatch && queryMatch[1]) {
            result.parameters = {
              query: queryMatch[1].trim(),
              resultsCount: 5
            };
          } else {
            // Default to using the original query
            result.parameters = {
              query: result.reasoning || "search query",
              resultsCount: 5
            };
          }
        } else if (result.selectedTool === 'RAG Query') {
          // Default parameters for RAG Query
          result.parameters = {
            query: llmContent.match(/query: "([^"]+)"/i)?.[1] || result.reasoning || "search query",
            useLLM: true
          };
        }
      }
    }
  } else if (result.selectedTool !== 'none' && result.selectedTool !== '') {
    // Create default parameters if none were provided
    if (result.selectedTool === 'Deep Research' || result.selectedTool === 'Web Search') {
      // Extract query from the LLM response if possible
      const queryMatch = llmContent.match(/query: "([^"]+)"/i) ||
                        llmContent.match(/query:\s*([^,\n]+)/i);

      if (queryMatch && queryMatch[1]) {
        result.parameters = {
          query: queryMatch[1].trim(),
          resultsCount: 5
        };
      } else {
        // Default to using the original query
        result.parameters = {
          query: result.reasoning || userData?.query || "search query",
          resultsCount: 5
        };
      }
    } else if (result.selectedTool === 'RAG Query') {
      // Default parameters for RAG Query
      result.parameters = {
        query: llmContent.match(/query: "([^"]+)"/i)?.[1] || result.reasoning || userData?.query || "search query",
        useLLM: true
      };
    }
  }

  // Extract direct response (if no tool was selected)
  const responseMatch = llmContent.match(/RESPONSE:\s*([\s\S]*?)$/i);
  if (responseMatch && responseMatch[1]) {
    result.response = responseMatch[1].trim();
  }

  console.log('Parsed result:', result);
  return result;
}

// Function to validate LLM response with a checkpoint
async function validateLLMResponse(originalQuery, response, toolUsed, relevanceScore) {
  try {
    console.log('Validating LLM response with checkpoint...');
    updateSystemState('lastQueryTime', new Date().toISOString());

    // Start a checkpoint operation if one doesn't exist
    if (!checkpointState.currentOperation) {
      startCheckpointOperation('validation', originalQuery, userSettings.defaultModel, {
        toolUsed: toolUsed,
        relevanceScore: relevanceScore
      });
    }

    // Update the checkpoint step
    updateCheckpointStep('validation');

    // Store the original response in the checkpoint state
    storeCheckpointResult('original_response', response);

    // Create a validation prompt
    const validationPrompt = `
I need you to evaluate whether the following response adequately answers the user's query.

USER QUERY: "${originalQuery}"

RESPONSE GENERATED:
${response}

TOOL USED: ${toolUsed}
RELEVANCE SCORE: ${relevanceScore}/10

${generateSystemContext()}

Please evaluate:
1. Does this response fully answer the user's query? (Yes/No)
2. Is the information accurate and relevant? (Yes/No)
3. Is additional information needed? (Yes/No)
4. If additional information is needed, which tool would be better to use?

Provide your assessment in this format:
COMPLETE: [Yes/No]
ACCURATE: [Yes/No]
NEEDS_MORE_INFO: [Yes/No]
RECOMMENDED_TOOL: [tool name or "none"]
CONFIDENCE: [1-10]
REASONING: [your detailed reasoning]
`;

    // Get the API key
    const apiKey = process.env.OPENROUTER_API_KEY || userSettings.openrouterApiKey;
    if (!apiKey) {
      console.log('No API key available for validation checkpoint');
      recordCheckpointError('No API key available');
      return { needsRework: false, enhancedResponse: response };
    }

    // Call the LLM with the validation prompt and retry logic
    console.log('Calling LLM for response validation with retry logic');

    // Ensure we respect the API cooldown
    console.log('Ensuring API cooldown period is respected before validation...');
    await apiCooldown.ensureCooldown();

    const { default: fetch } = await import('node-fetch');

    // Retry configuration with much longer delays
    const maxRetries = 3;
    let retries = 0;
    let delay = 10000; // Start with 10 seconds delay (5x the original)
    let llmResponse;

    while (retries <= maxRetries) {
      try {
        llmResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`,
            'HTTP-Referer': 'http://localhost:3000',
            'X-Title': 'Fungi Response Validation'
          },
          body: JSON.stringify({
            model: userSettings.defaultModel,
            messages: [
              { role: 'user', content: validationPrompt }
            ]
          })
        });

        if (llmResponse.ok) {
          // Success, break out of retry loop
          console.log('LLM API call successful');
          break;
        } else if (llmResponse.status === 429) {
          // Rate limit hit, retry with exponential backoff
          retries++;
          if (retries > maxRetries) {
            console.error(`Rate limit hit, max retries (${maxRetries}) exceeded`);
            recordCheckpointError(`Rate limit hit, max retries (${maxRetries}) exceeded`);
            return { needsRework: false, enhancedResponse: response };
          }

          console.log(`Rate limit hit (429), retrying in ${delay/1000} seconds... (Attempt ${retries} of ${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, delay));
          delay *= 2; // Exponential backoff
        } else {
          // Other error, don't retry
          const errorMessage = `LLM API error: ${llmResponse.status}`;
          console.error(errorMessage);
          recordCheckpointError(errorMessage);

          // Try to recover from the error
          const recovery = await recoverFromError(new Error(errorMessage));

          if (recovery.action === 'retry_step') {
            console.log('Recovery suggests retrying validation with modified parameters');
            // We could implement parameter modifications here if needed
            continue;
          } else {
            // For other recovery actions, just return the original response
            return { needsRework: false, enhancedResponse: response };
          }
        }
      } catch (error) {
        // Network error or other exception
        retries++;
        if (retries > maxRetries) {
          console.error(`API call failed, max retries (${maxRetries}) exceeded:`, error);
          recordCheckpointError(`API call failed: ${error.message}`);
          return { needsRework: false, enhancedResponse: response };
        }

        console.log(`API call failed, retrying in ${delay/1000} seconds... (Attempt ${retries} of ${maxRetries})`);
        recordCheckpointError(`API call failed (attempt ${retries}): ${error.message}`);
        await new Promise(resolve => setTimeout(resolve, delay));
        delay *= 2; // Exponential backoff
      }
    }

    if (!llmResponse || !llmResponse.ok) {
      const errorMessage = 'LLM API error after retries';
      console.error(errorMessage);
      recordCheckpointError(errorMessage);
      return { needsRework: false, enhancedResponse: response };
    }

    const validationResult = await llmResponse.json();
    const validationContent = validationResult.choices[0].message.content;

    console.log('Validation response:', validationContent);

    // Store the validation response in the checkpoint state
    storeCheckpointResult('validation_response', validationContent);

    // Parse the validation response
    const validation = parseValidationResponse(validationContent);

    // Store the parsed results in the checkpoint state
    storeCheckpointResult('validation_parsed', validation);

    // If the response is incomplete or needs more info, take action
    if (!validation.complete || validation.needsMoreInfo) {
      console.log('Response validation indicates more information is needed');

      if (validation.recommendedTool && validation.recommendedTool !== 'none' &&
          validation.recommendedTool !== toolUsed) {
        console.log(`Validation suggests using ${validation.recommendedTool} instead`);

        // Update the checkpoint step to indicate tool switching
        updateCheckpointStep('tool_switching');
        storeCheckpointResult('new_tool', validation.recommendedTool);

        // Switch to the recommended tool
        return {
          needsRework: true,
          recommendedTool: validation.recommendedTool,
          reason: validation.reasoning,
          confidence: validation.confidence
        };
      } else {
        // Just add the validation feedback to the response
        updateCheckpointStep('response_enhancement');
        storeCheckpointResult('enhanced_response', `${response}\n\n[Note: The system has identified that this response may not fully answer your question. ${validation.reasoning}]`);

        return {
          needsRework: false,
          enhancedResponse: `${response}\n\n[Note: The system has identified that this response may not fully answer your question. ${validation.reasoning}]`
        };
      }
    }

    // Response is good, mark this operation as complete
    updateCheckpointStep('complete');

    // Generate a summary of this operation for future learning
    setTimeout(() => {
      generateOperationSummary().catch(error => {
        console.error('Error generating operation summary:', error);
      });
    }, 1000);

    // Response is good, return as is
    return {
      needsRework: false,
      enhancedResponse: response
    };
  } catch (error) {
    console.error('Error validating LLM response:', error);
    recordCheckpointError(`Validation error: ${error.message}`);

    // Try to recover from the error
    const recovery = await recoverFromError(error);

    if (recovery.action === 'retry_step') {
      console.log('Recovery suggests retrying validation');
      // We could implement a retry here, but for simplicity, we'll just return the original response
      return { needsRework: false, enhancedResponse: response };
    }

    // On error, return the original response
    return {
      needsRework: false,
      enhancedResponse: response
    };
  }
}

// Helper function to parse the validation response
function parseValidationResponse(validationResponse) {
  const result = {
    complete: true,
    accurate: true,
    needsMoreInfo: false,
    recommendedTool: 'none',
    confidence: 5,
    reasoning: ''
  };

  // Extract values using regex
  const completeMatch = validationResponse.match(/COMPLETE:\s*(Yes|No)/i);
  if (completeMatch) result.complete = completeMatch[1].toLowerCase() === 'yes';

  const accurateMatch = validationResponse.match(/ACCURATE:\s*(Yes|No)/i);
  if (accurateMatch) result.accurate = accurateMatch[1].toLowerCase() === 'yes';

  const needsMoreMatch = validationResponse.match(/NEEDS_MORE_INFO:\s*(Yes|No)/i);
  if (needsMoreMatch) result.needsMoreInfo = needsMoreMatch[1].toLowerCase() === 'yes';

  const toolMatch = validationResponse.match(/RECOMMENDED_TOOL:\s*([a-zA-Z_\s]+)/i);
  if (toolMatch) result.recommendedTool = toolMatch[1].toLowerCase().trim();

  const confidenceMatch = validationResponse.match(/CONFIDENCE:\s*(\d+)/i);
  if (confidenceMatch) result.confidence = parseInt(confidenceMatch[1], 10);

  const reasoningMatch = validationResponse.match(/REASONING:\s*([\s\S]*?)$/i);
  if (reasoningMatch) result.reasoning = reasoningMatch[1].trim();

  return result;
}

// Helper function to execute the selected tool
async function executeSelectedTool(toolSelection, userData) {
  // Update system state
  updateSystemState('lastToolUsed', toolSelection.selectedTool.toLowerCase());
  updateSystemState('lastQueryTime', new Date().toISOString());

  const toolName = toolSelection.selectedTool.toLowerCase();
  const params = toolSelection.parameters || {};

  console.log(`Executing tool: ${toolName} with parameters:`, params);
  mainWindow.webContents.send('log', `Executing tool: ${toolName}`);

  try {
    // Execute the appropriate tool based on the name
    if (toolName.includes('conversation')) {
      // Handle Conversation tool - just return the response directly
      console.log('Executing Conversation tool');
      mainWindow.webContents.send('log', 'Executing Conversation tool');

      // Store the user message in conversation history
      if (conversationManager) {
        try {
          await conversationManager.addMessage('user', `<p>${userData.query}</p>`);
          console.log('User message stored in conversation history');
        } catch (error) {
          console.error('Error storing user message in conversation history:', error);
        }
      }

      // Generate a response
      const response = toolSelection.response || 'I\'m happy to chat with you! What would you like to talk about?';

      // Store the assistant response in conversation history
      if (conversationManager) {
        try {
          await conversationManager.addMessage('assistant', response);
          console.log('Assistant response stored in conversation history');
        } catch (error) {
          console.error('Error storing assistant response in conversation history:', error);
        }
      }

      // Send a signal to re-enable the input field
      mainWindow.webContents.send('rag-query-complete');

      return {
        type: 'direct_response',
        content: response
      };
    }
    else if (toolName.includes('web search') || toolName.includes('deep research')) {
      // Execute Deep Research
      const resultsCount = params.resultsCount || 3;
      const query = params.query || userData.query;

      console.log(`Executing Deep Research for query: "${query}" with ${resultsCount} results`);
      mainWindow.webContents.send('log', `Executing Deep Research for query: "${query}" with ${resultsCount} results`);

      return await executeFullDeepResearch(query, userData.model, {
        resultsCount: userSettings.deepResearchDefaultResultsPerQuery || 5,
        maxSubQueries: userSettings.deepResearchMaxSubQueries || 5,
        urlTimeout: userSettings.deepResearchUrlTimeout || 10000,
        saveToDatabase: false
      });
    }
    else if (toolName.includes('document analysis')) {
      // Execute Document Analysis
      // This requires a document to be attached, which we don't have in this flow
      // Return an instruction for the user
      return {
        type: 'instruction',
        content: 'To analyze a document, please attach it using the attachment button in the chat interface.'
      };
    }
    else if (toolName.includes('rag query')) {
      // Execute RAG Query
      const useLLM = params.useLLM !== false;
      const query = params.query || userData.query;

      console.log(`Executing RAG Query for query: "${query}" with useLLM=${useLLM}`);
      mainWindow.webContents.send('log', `Executing RAG Query for query: "${query}" with useLLM=${useLLM}`);

      // Check if we have a relevancy score from the initial RAG query
      if (userData.ragRelevanceScore !== undefined && userData.ragRelevanceScore < 5) {
        console.log(`Initial RAG relevance score ${userData.ragRelevanceScore}/10 is below threshold (5). Automatically switching to Deep Research.`);
        mainWindow.webContents.send('log', `Low relevance score (${userData.ragRelevanceScore}/10). Automatically switching to Deep Research.`);

        // Execute Deep Research directly instead of RAG Query using full multi-query implementation
        try {
          console.log('Executing full Deep Research with multi-query support from intelligent mode');
          mainWindow.webContents.send('log', 'Executing full Deep Research with multi-query support from intelligent mode');

          // Use the full deep research implementation with proper settings
          return await executeFullDeepResearch(query, userData.model, {
            resultsCount: userSettings.deepResearchDefaultResultsPerQuery || 5,
            maxSubQueries: userSettings.deepResearchMaxSubQueries || 5,
            urlTimeout: userSettings.deepResearchUrlTimeout || 10000,
            saveToDatabase: false // Always false to ensure fresh results
          });
        } catch (deepResearchError) {
          console.error('Error executing Deep Research after low initial RAG relevance:', deepResearchError);

          // Return a special tool_error type
          return {
            type: 'tool_error',
            content: `I couldn't find relevant information about this topic. The knowledge base has low relevance (${userData.ragRelevanceScore}/10) and Deep Research encountered an error: ${deepResearchError.message}`,
            originalQuery: query
          };
        }
      }

      // If no initial relevancy score or score is acceptable, proceed with RAG Query
      return await executeRagQuery(query, useLLM, userData.model);
    }
    else if (toolName.includes('file import')) {
      // File Import requires UI interaction
      return {
        type: 'instruction',
        content: 'To import files, please use the Import button in the sidebar or the attachment button in the chat interface.'
      };
    }
    else if (toolName.includes('pinecone') || toolName.includes('cloud storage')) {
      // Configure Pinecone
      const apiKey = params.apiKey || userSettings.pineconeApiKey;
      const environment = params.environment || userSettings.pineconeEnvironment;
      const indexName = params.indexName || userSettings.pineconeIndexName;
      const usePinecone = params.usePinecone !== false;

      if (!apiKey || !indexName) {
        return {
          type: 'instruction',
          content: 'To configure Pinecone, please provide your API key and index name in the Settings tab.'
        };
      }

      console.log(`Configuring Pinecone with index: ${indexName}`);
      mainWindow.webContents.send('log', `Configuring Pinecone with index: ${indexName}`);

      return await configurePinecone(apiKey, environment, indexName, usePinecone);
    }
    else {
      // Unknown tool
      console.error(`Unknown tool: ${toolName}`);
      return {
        type: 'error',
        content: `Unknown tool: ${toolName}. Please try a different approach.`
      };
    }
  } catch (error) {
    console.error(`Error executing tool ${toolName}:`, error);
    mainWindow.webContents.send('log', `Error executing tool ${toolName}: ${error.message}`);
    return {
      type: 'error',
      content: `Error executing ${toolName}: ${error.message}`
    };
  }
}

// Helper function to execute Full Deep Research with multi-query support (for intelligent mode)
async function executeFullDeepResearch(query, model, options = {}) {
  const {
    resultsCount = 5,
    maxSubQueries = 5,
    urlTimeout = 10000,
    saveToDatabase = false
  } = options;

  console.log(`Executing Full Deep Research: "${query}" with ${resultsCount} results, ${maxSubQueries} max sub-queries`);
  mainWindow.webContents.send('log', `Starting multi-query research for: ${query}`);
  mainWindow.webContents.send('log', `Multi-query settings: ${maxSubQueries} max sub-queries, ${urlTimeout/1000}s URL timeout`);

  try {
    // STEP 1: Generate sub-queries using LLM
    const subQueries = await generateSubQueries(query, maxSubQueries, model);

    if (!subQueries || subQueries.length === 0) {
      // Fallback to single query if sub-query generation fails
      mainWindow.webContents.send('log', 'Sub-query generation failed, falling back to single query');
      const searchResults = await performWebSearch(query, resultsCount);
      return await processSingleQueryResults({ query, model, resultsCount }, searchResults);
    }

    mainWindow.webContents.send('log', `Generated ${subQueries.length} focused search queries`);

    // STEP 2: Perform parallel searches for all sub-queries
    const allSearchResults = [];
    const allExtractedContents = [];

    for (let i = 0; i < subQueries.length; i++) {
      const subQuery = subQueries[i];
      mainWindow.webContents.send('log', `Searching query ${i + 1}/${subQueries.length}: "${subQuery}"`);

      try {
        const searchResults = await performWebSearch(subQuery, resultsCount);
        if (searchResults && searchResults.length > 0) {
          // Extract content from search results with timeout handling
          const extractedContents = await extractContentWithTimeout(searchResults, subQuery, i + 1, subQueries.length, urlTimeout);

          allSearchResults.push(...searchResults);
          allExtractedContents.push(...extractedContents);
        }
      } catch (error) {
        console.warn(`Error searching sub-query "${subQuery}":`, error.message);
        mainWindow.webContents.send('log', `Warning: Search failed for query ${i + 1}: ${error.message}`);
      }
    }

    // Remove duplicates based on URL
    const uniqueResults = removeDuplicateResults(allSearchResults, allExtractedContents);
    const finalSearchResults = uniqueResults.searchResults;
    const finalExtractedContents = uniqueResults.extractedContents;

    mainWindow.webContents.send('log', `Collected ${finalSearchResults.length} unique sources from ${subQueries.length} searches`);

    if (!finalSearchResults || finalSearchResults.length === 0) {
      throw new Error('No search results found for this query');
    }

    // STEP 3: Skip gap analysis to reduce API calls and avoid rate limiting
    console.log('Skipping gap analysis to reduce API calls and avoid rate limiting');
    mainWindow.webContents.send('log', 'Skipping gap analysis to reduce API calls');

    // TODO: Re-enable gap analysis once rate limiting is resolved
    // const gapAnalysis = await analyzeContentGaps(query, finalExtractedContents, model);

    if (false) { // Disabled gap analysis
      const remainingQueries = maxSubQueries - subQueries.length;
      const followUpQueries = []; // gapAnalysis.additionalQueries.slice(0, remainingQueries);

      if (followUpQueries.length > 0) {
        mainWindow.webContents.send('log', `Analyzing gaps, adding ${followUpQueries.length} follow-up queries`);

        // Perform follow-up searches
        for (let i = 0; i < followUpQueries.length; i++) {
          const followUpQuery = followUpQueries[i];
          mainWindow.webContents.send('log', `Follow-up query ${i + 1}/${followUpQueries.length}: "${followUpQuery}"`);

          try {
            const searchResults = await performWebSearch(followUpQuery, resultsCount);
            if (searchResults && searchResults.length > 0) {
              const extractedContents = await extractContentWithTimeout(searchResults, followUpQuery, subQueries.length + i + 1, subQueries.length + followUpQueries.length, urlTimeout);

              allSearchResults.push(...searchResults);
              allExtractedContents.push(...extractedContents);
            }
          } catch (error) {
            console.warn(`Error searching follow-up query "${followUpQuery}":`, error.message);
            mainWindow.webContents.send('log', `Warning: Follow-up search failed for query ${i + 1}: ${error.message}`);
          }
        }

        // Remove duplicates again after follow-up queries
        const finalUniqueResults = removeDuplicateResults(allSearchResults, allExtractedContents);
        finalSearchResults.length = 0;
        finalExtractedContents.length = 0;
        finalSearchResults.push(...finalUniqueResults.searchResults);
        finalExtractedContents.push(...finalUniqueResults.extractedContents);

        mainWindow.webContents.send('log', `Final collection: ${finalSearchResults.length} unique sources after follow-up searches`);
      }
    }

    // STEP 4: Sequential analysis with progressive summarization
    console.log(`Starting sequential analysis of ${finalExtractedContents.length} sources`);
    mainWindow.webContents.send('log', `Analyzing ${finalExtractedContents.length} sources individually...`);

    const individualSummaries = [];

    // Analyze each source individually
    for (let i = 0; i < finalExtractedContents.length; i++) {
      const content = finalExtractedContents[i];
      const sourceNumber = i + 1;

      console.log(`Analyzing source ${sourceNumber}/${finalExtractedContents.length}: ${content.title}`);
      mainWindow.webContents.send('log', `Analyzing source ${sourceNumber}/${finalExtractedContents.length}: ${content.title}`);

      try {
        const individualAnalysis = await analyzeIndividualSource(content, query, model, sourceNumber, finalExtractedContents.length);

        if (individualAnalysis && individualAnalysis.trim()) {
          individualSummaries.push({
            sourceNumber: sourceNumber,
            title: content.title,
            url: content.url,
            summary: individualAnalysis
          });

          console.log(`✓ Source ${sourceNumber} analyzed successfully`);
          mainWindow.webContents.send('log', `✓ Source ${sourceNumber} analyzed successfully`);
        } else {
          console.warn(`⚠ Source ${sourceNumber} analysis returned empty result`);
          mainWindow.webContents.send('log', `⚠ Source ${sourceNumber} analysis was empty`);
        }

        // Small delay between analyses to avoid rate limiting
        if (i < finalExtractedContents.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 3000)); // 3 second delay
        }

      } catch (error) {
        console.error(`Error analyzing source ${sourceNumber}:`, error.message);
        mainWindow.webContents.send('log', `Error analyzing source ${sourceNumber}: ${error.message}`);

        // Continue with other sources even if one fails
        continue;
      }
    }

    console.log(`Individual analysis complete. ${individualSummaries.length}/${finalExtractedContents.length} sources analyzed successfully`);
    mainWindow.webContents.send('log', `Individual analysis complete. ${individualSummaries.length} sources analyzed successfully`);

    if (individualSummaries.length === 0) {
      throw new Error('No sources could be analyzed successfully');
    }

    // STEP 5: Final synthesis of all summaries
    console.log('Starting final synthesis of all summaries...');
    mainWindow.webContents.send('log', 'Synthesizing all source analyses...');

    const analysis = await synthesizeAllSummaries(individualSummaries, query, model);

    mainWindow.webContents.send('log', 'Multi-query analysis complete');

    // Return the analysis and sources
    return {
      type: 'deep_research',
      content: analysis,
      sources: finalSearchResults
    };
  } catch (error) {
    console.error('Error in Full Deep Research:', error);
    mainWindow.webContents.send('log', `Error in Full Deep Research: ${error.message}`);
    throw error;
  }
}

// Helper function to execute Deep Research
async function executeDeepResearch(query, resultsCount, model) {
  console.log(`Executing Deep Research: "${query}" with ${resultsCount} results`);
  mainWindow.webContents.send('log', `Executing Deep Research: "${query}" with ${resultsCount} results`);

  try {
    // Start a checkpoint operation
    startCheckpointOperation('deep_research', query, model || userSettings.defaultModel, {
      resultsCount: resultsCount
    });

    // Update the checkpoint step
    updateCheckpointStep('web_search');

    // Use the original query without special case handling
    let searchQuery = query;

    // Perform the web search
    const searchResults = await performWebSearch(searchQuery, resultsCount);

    if (!searchResults || searchResults.length === 0) {
      const error = new Error('No search results found for this query');
      recordCheckpointError(error.message, 'web_search');

      // Try to recover from the error
      const recovery = await recoverFromError(error);

      if (recovery.action === 'retry_step') {
        // Try a different search query if suggested
        if (recovery.parameters && recovery.parameters.searchQuery) {
          console.log(`Recovery suggests using a different search query: "${recovery.parameters.searchQuery}"`);
          searchQuery = recovery.parameters.searchQuery;

          // Try the search again with the new query
          const newSearchResults = await performWebSearch(searchQuery, resultsCount);
          if (newSearchResults && newSearchResults.length > 0) {
            storeCheckpointResult('search_results', newSearchResults);
            storeCheckpointResult('modified_search_query', searchQuery);

            // Continue with the new search results
            console.log(`Found ${newSearchResults.length} search results for modified query: ${searchQuery}`);
            mainWindow.webContents.send('log', `Found ${newSearchResults.length} search results for modified query: ${searchQuery}`);

            // Use the new search results
            searchResults = newSearchResults;
          } else {
            throw error; // Still no results, throw the original error
          }
        } else {
          throw error;
        }
      } else if (recovery.action === 'alternative_approach') {
        // If recovery suggests an alternative approach like using RAG Query
        if (recovery.instructions && recovery.instructions.toLowerCase().includes('rag query')) {
          console.log('Recovery suggests using RAG Query instead');

          // Execute RAG Query directly
          const ragResult = await executeRagQuery(query, true, model);

          // Add a note about the tool switch
          if (ragResult && ragResult.content) {
            ragResult.content = `[Note: Deep Research found no search results, so the system automatically switched to using the knowledge base.]\n\n${ragResult.content}`;
          }

          return ragResult;
        } else {
          throw error;
        }
      } else {
        throw error;
      }
    }

    // Store the search results in the checkpoint state
    storeCheckpointResult('search_results', searchResults);

    console.log(`Found ${searchResults.length} search results for query: ${searchQuery}`);
    mainWindow.webContents.send('log', `Found ${searchResults.length} search results for query: ${searchQuery}`);

    // Update the checkpoint step
    updateCheckpointStep('content_extraction');

    // Extract content from each search result
    const extractedContents = [];
    for (const result of searchResults) {
      try {
        console.log(`Extracting content from: ${result.url}`);
        const extractedContent = await extractContentFromUrl(result.url);
        extractedContents.push(extractedContent);
      } catch (error) {
        console.error(`Error extracting content from ${result.url}:`, error.message);
        mainWindow.webContents.send('log', `Error extracting content from ${result.url}: ${error.message}`);
        recordCheckpointError(`Error extracting content from ${result.url}: ${error.message}`, 'content_extraction');
      }
    }

    if (extractedContents.length === 0) {
      const error = new Error('Could not extract content from any search results');
      recordCheckpointError(error.message, 'content_extraction');

      // Try to recover from the error
      const recovery = await recoverFromError(error);

      if (recovery.action === 'alternative_approach') {
        // If recovery suggests an alternative approach like using RAG Query
        if (recovery.instructions && recovery.instructions.toLowerCase().includes('rag query')) {
          console.log('Recovery suggests using RAG Query instead');

          // Execute RAG Query directly
          const ragResult = await executeRagQuery(query, true, model);

          // Add a note about the tool switch
          if (ragResult && ragResult.content) {
            ragResult.content = `[Note: Deep Research couldn't extract content from search results, so the system automatically switched to using the knowledge base.]\n\n${ragResult.content}`;
          }

          return ragResult;
        } else {
          throw error;
        }
      } else {
        throw error;
      }
    }

    // Store the extracted contents in the checkpoint state
    storeCheckpointResult('extracted_contents', extractedContents);

    console.log(`Extracted content from ${extractedContents.length} search results`);
    mainWindow.webContents.send('log', `Extracted content from ${extractedContents.length} search results`);

    // Update the checkpoint step
    updateCheckpointStep('content_filtering');

    // Filter out low-quality content
    const filteredContents = extractedContents.filter(item =>
      item.content && item.content.length > 100 &&
      !item.content.includes('Error during content extraction') &&
      !item.content.includes('Could not access this page')
    );

    if (filteredContents.length === 0) {
      // If all content was filtered out, use the original content
      console.log('All content was filtered out, using original content');
      mainWindow.webContents.send('log', 'All content was filtered out, using original content');
      recordCheckpointError('All content was filtered out, using original content', 'content_filtering');
    }

    // Use filtered content if available, otherwise use original
    const contentToAnalyze = filteredContents.length > 0 ? filteredContents : extractedContents;

    // Store the filtered contents in the checkpoint state
    storeCheckpointResult('filtered_contents', filteredContents);
    storeCheckpointResult('content_to_analyze', contentToAnalyze);

    // Update the checkpoint step
    updateCheckpointStep('llm_analysis');

    // Create an improved prompt for all queries
    const prompt = `You are a research assistant analyzing web search results about: "${query}"

I've collected information from ${contentToAnalyze.length} sources:
${contentToAnalyze.map((item) => `- ${item.title} (${item.url})`).join('\n')}

Below is the content from these sources:

${contentToAnalyze.map((item) => `### ${item.title} (${item.url})\n${item.content}\n\n`).join('---\n\n')}

Based on the information above, please provide a comprehensive answer to the query: "${query}"

Your response should:
1. Start with a clear, direct answer to the query
2. Be well-structured with headings and bullet points where appropriate
3. Synthesize information from multiple sources into coherent paragraphs
4. Highlight key points and important details
5. Cite specific sources when providing important information
6. Acknowledge any contradictions or uncertainties in the information
7. Provide a balanced perspective on the topic

IMPORTANT FORMATTING INSTRUCTIONS:
- Make sure your response is properly formatted with complete sentences and paragraphs
- Ensure all text is coherent and flows logically
- Do not include incomplete sentences or fragments
- Verify that your response directly addresses the user's query with specific information
- If the query is about recommendations or "best" options, include a "Top Recommendations" section

IMPORTANT CONTENT EVALUATION:
- If the search results don't contain enough information to answer the query, please state this clearly
- If the information is insufficient, begin your response with: "INSUFFICIENT_INFORMATION: I don't have enough data to properly answer this query."
- If you believe a different tool would be more appropriate, suggest it like this: "SUGGESTED_TOOL: [tool name] - [reason]"
- For cooking recipes, how-to guides, or specific instructions, suggest using RAG Query if the web results are inadequate
- For general knowledge questions that might be in a knowledge base, suggest using RAG Query if appropriate

${generateSystemContext()}`;

    // Store the prompt in the checkpoint state
    storeCheckpointResult('llm_prompt', prompt);

    // Get the API key
    const apiKey = process.env.OPENROUTER_API_KEY || userSettings.openrouterApiKey;
    if (!apiKey) {
      const error = new Error('API key is required for Deep Research. Please set it in the settings.');
      recordCheckpointError(error.message, 'llm_analysis');
      throw error;
    }

    console.log('Calling LLM for analysis...');
    mainWindow.webContents.send('log', 'Calling LLM to analyze search results...');

    // Create a standard system message
    const systemMessage = 'You are a helpful research assistant that provides comprehensive answers based on the provided web search results.';

    // Call the LLM API with retry logic
    const { default: fetch } = await import('node-fetch');

    // Ensure we respect the API cooldown
    console.log('Ensuring API cooldown period is respected before LLM analysis...');
    await apiCooldown.ensureCooldown();

    // Retry configuration
    const maxRetries = 3;
    let retries = 0;
    let delay = 2000; // Start with 2 seconds delay
    let response;

    while (retries <= maxRetries) {
      try {
        response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`,
            'HTTP-Referer': 'http://localhost:3000',
            'X-Title': 'Fungi Deep Research'
          },
          body: JSON.stringify({
            model: model || userSettings.defaultModel,
            messages: [
              { role: 'system', content: systemMessage },
              { role: 'user', content: prompt }
            ]
          })
        });

        if (response.ok) {
          // Success, break out of retry loop
          console.log('LLM API call successful');
          break;
        } else if (response.status === 429) {
          // Rate limit hit, retry with exponential backoff
          retries++;
          if (retries > maxRetries) {
            console.error(`Rate limit hit, max retries (${maxRetries}) exceeded`);
            recordCheckpointError(`Rate limit hit, max retries (${maxRetries}) exceeded`, 'llm_analysis');
            throw new Error(`LLM API rate limit hit, max retries (${maxRetries}) exceeded`);
          }

          console.log(`Rate limit hit (429), retrying in ${delay/1000} seconds... (Attempt ${retries} of ${maxRetries})`);
          recordCheckpointError(`Rate limit hit (429), retrying in ${delay/1000} seconds... (Attempt ${retries} of ${maxRetries})`, 'llm_analysis');
          await new Promise(resolve => setTimeout(resolve, delay));
          delay *= 2; // Exponential backoff
        } else {
          // Other error, don't retry
          const errorText = await response.text();
          const errorMessage = `LLM API error: ${response.status} ${errorText}`;
          console.error(errorMessage);
          recordCheckpointError(errorMessage, 'llm_analysis');

          // Try to recover from the error
          const recovery = await recoverFromError(new Error(errorMessage));

          if (recovery.action === 'retry_step') {
            console.log('Recovery suggests retrying LLM analysis');
            continue;
          } else {
            throw new Error(errorMessage);
          }
        }
      } catch (error) {
        // Network error or other exception
        retries++;
        if (retries > maxRetries) {
          console.error(`API call failed, max retries (${maxRetries}) exceeded:`, error);
          recordCheckpointError(`API call failed: ${error.message}`, 'llm_analysis');
          throw error;
        }

        console.log(`API call failed, retrying in ${delay/1000} seconds... (Attempt ${retries} of ${maxRetries})`);
        recordCheckpointError(`API call failed (attempt ${retries}): ${error.message}`, 'llm_analysis');
        await new Promise(resolve => setTimeout(resolve, delay));
        delay *= 2; // Exponential backoff
      }
    }

    if (!response || !response.ok) {
      const errorMessage = 'LLM API error after retries';
      console.error(errorMessage);
      recordCheckpointError(errorMessage, 'llm_analysis');
      throw new Error(errorMessage);
    }

    const llmResponse = await response.json();
    const analysis = llmResponse.choices[0].message.content;

    // Store the analysis in the checkpoint state
    storeCheckpointResult('llm_analysis', analysis);

    console.log('Analysis complete');
    mainWindow.webContents.send('log', 'Analysis of search results complete');

    // Update the checkpoint step
    updateCheckpointStep('validation');

    // Add a much longer delay before validation to avoid rate limiting
    console.log('Adding delay before validation to avoid rate limiting (30 seconds)...');
    await new Promise(resolve => setTimeout(resolve, 30000)); // 30 second delay (10x the original)

    // Validate the response with a checkpoint
    const validation = await validateLLMResponse(query, analysis, 'deep_research', 8);

    // Store the validation result in the checkpoint state
    storeCheckpointResult('validation_result', validation);

    // Check if validation failed due to rate limits
    if (checkpointState.errors.some(error =>
      (typeof error === 'string' && error.includes('Rate limit hit, max retries')) ||
      (error && error.message && typeof error.message === 'string' && error.message.includes('Rate limit hit, max retries'))
    )) {
      console.log('Validation failed due to rate limits, but we will still use the analysis');
      mainWindow.webContents.send('log', 'Validation failed due to rate limits, but we will still use the analysis');

      // Add a note to the analysis about the validation failure
      const validationFailureNote = '\n\n[Note: The system was unable to validate this response due to API rate limits. The information provided may still be useful, but has not undergone validation.]';
      analysis += validationFailureNote;
    }

    // Update the checkpoint step
    updateCheckpointStep('complete');

    // Generate a summary of this operation for future learning with a longer delay
    // to avoid rate limits
    setTimeout(() => {
      generateOperationSummary().catch(error => {
        console.error('Error generating operation summary:', error);
      });
    }, 30000); // 30 second delay (30x the original)

    // Return the analysis and sources
    return {
      type: 'deep_research',
      content: validation.enhancedResponse || analysis,
      sources: searchResults
    };
  } catch (error) {
    console.error('Error in Deep Research:', error);
    mainWindow.webContents.send('log', `Error in Deep Research: ${error.message}`);
    recordCheckpointError(`Deep Research error: ${error.message}`);

    // Try to recover from the error
    const recovery = await recoverFromError(error);

    if (recovery.action === 'alternative_approach') {
      // If recovery suggests an alternative approach like using RAG Query
      if (recovery.instructions && recovery.instructions.toLowerCase().includes('rag query')) {
        console.log('Recovery suggests using RAG Query instead');

        // Execute RAG Query directly
        try {
          const ragResult = await executeRagQuery(query, true, model);

          // Add a note about the tool switch
          if (ragResult && ragResult.content) {
            ragResult.content = `[Note: Deep Research encountered an error (${error.message}), so the system automatically switched to using the knowledge base.]\n\n${ragResult.content}`;
          }

          return ragResult;
        } catch (ragError) {
          console.error('Error executing RAG Query after Deep Research error:', ragError);
          throw error; // If RAG Query also fails, throw the original error
        }
      }
    }

    throw error;
  }
}

// Helper function to execute CONTENT RAG Query
async function executeRagQuery(query, useLLM, model) {
  console.log(`Executing CONTENT RAG Query: "${query}" with useLLM=${useLLM}`);
  mainWindow.webContents.send('log', `Executing content knowledge base query: "${query}" with useLLM=${useLLM}`);

  // Create a promise that will be resolved when the rag-answer event is received
  return new Promise((resolve, reject) => {
    // First, we need to check the relevance score before using the LLM
    // We'll do this by querying the RAG system in retrieval-only mode first
    console.log('First checking content relevance score in retrieval-only mode...');

    // Set a flag to track if we're in the relevance check phase
    let checkingRelevance = true;

    // Add error handling for Python errors
    const handlePythonError = (error) => {
      console.error('Python error in RAG query:', error);
      mainWindow.webContents.send('log', `Error in RAG query: ${error}`);

      // Clear the Intelligent Mode error status
      mainWindow.webContents.send('intelligent-mode-error', false);

      // Reject with a more user-friendly error message
      reject(new Error(`Error querying the knowledge base: ${error}`));
    };

    // This code is no longer needed as we're now using the rag-relevance-checked event
    // We'll keep a placeholder function for backward compatibility
    const relevanceCheckListener = (event, initialResponse) => {
      console.log('WARNING: Using deprecated relevanceCheckListener. This should not be called anymore.');
      // This function is kept for backward compatibility but should not be called
    };

    // Set up a one-time listener for errors
    const errorListener = (event, error) => {
      console.log('CONTENT RAG error received:', error);

      // Remove the appropriate listener based on the current phase
      if (checkingRelevance) {
        mainWindow.webContents.removeListener('rag-answer', relevanceCheckListener);
      }

      mainWindow.webContents.removeListener('rag-error', errorListener);

      // If we're in the relevance check phase, reject with the error
      if (checkingRelevance) {
        reject(error);
        return;
      }

      // Otherwise, try to get error handling guidance from the RAG system
      handleErrorWithRAG(error, query, useLLM)
        .then(errorGuidance => {
          // Automatically switch to Deep Research
          console.log('RAG query error, automatically switching to Deep Research');
          mainWindow.webContents.send('log', 'RAG query error, automatically switching to Deep Research');

          // Execute Deep Research directly
          executeFullDeepResearch(query, model, {
            resultsCount: userSettings.deepResearchDefaultResultsPerQuery || 5,
            maxSubQueries: userSettings.deepResearchMaxSubQueries || 5,
            urlTimeout: userSettings.deepResearchUrlTimeout || 10000,
            saveToDatabase: false
          })
            .then(deepResearchResult => {
              resolve(deepResearchResult);
            })
            .catch(deepResearchError => {
              console.error('Error executing Deep Research after RAG error:', deepResearchError);
              // If Deep Research fails, fall back to the original suggestion
              resolve({
                type: 'tool_error',
                content: `I encountered an issue while searching the knowledge base. ${errorGuidance}\n\nI tried to use Deep Research automatically but encountered an error: ${deepResearchError.message}`,
                originalQuery: query
              });
            });
        })
        .catch(ragError => {
          // If the RAG error handling itself failed, still execute Deep Research
          console.error('Error handling with RAG failed:', ragError);

          // Execute Deep Research directly
          executeFullDeepResearch(query, model, {
            resultsCount: userSettings.deepResearchDefaultResultsPerQuery || 5,
            maxSubQueries: userSettings.deepResearchMaxSubQueries || 5,
            urlTimeout: userSettings.deepResearchUrlTimeout || 10000,
            saveToDatabase: false
          })
            .then(deepResearchResult => {
              resolve(deepResearchResult);
            })
            .catch(deepResearchError => {
              console.error('Error executing Deep Research after RAG error:', deepResearchError);
              // If Deep Research fails, fall back to the original suggestion
              resolve({
                type: 'tool_error',
                content: `I encountered an issue while searching the knowledge base. I tried to use Deep Research automatically but encountered an error: ${deepResearchError.message}`,
                originalQuery: query
              });
            });
        });
    };

    // Set a timeout to prevent hanging
    const timeout = setTimeout(() => {
      console.log('CONTENT RAG query timeout reached');

      // Remove the appropriate listener based on the current phase
      if (checkingRelevance) {
        mainWindow.webContents.removeListener('rag-answer', relevanceCheckListener);
      }

      mainWindow.webContents.removeListener('rag-error', errorListener);

      // Generate a timeout summary for the RAG system
      const timeoutSummary = `Timeout occurred while executing CONTENT RAG query: "${query}" with useLLM=${useLLM} and model=${model || userSettings.defaultModel}. The operation took longer than 60 seconds to complete.`;

      // Try to get timeout handling guidance from the RAG system
      handleTimeoutWithRAG(timeoutSummary, query)
        .then(timeoutGuidance => {
          // Log the guidance we received
          console.log('Timeout guidance from RAG:', timeoutGuidance);

          // Automatically switch to Deep Research
          console.log('RAG query timed out, automatically switching to Deep Research');
          mainWindow.webContents.send('log', 'RAG query timed out, automatically switching to Deep Research');

          // Execute Deep Research directly
          executeFullDeepResearch(query, model, {
            resultsCount: userSettings.deepResearchDefaultResultsPerQuery || 5,
            maxSubQueries: userSettings.deepResearchMaxSubQueries || 5,
            urlTimeout: userSettings.deepResearchUrlTimeout || 10000,
            saveToDatabase: false
          })
            .then(deepResearchResult => {
              resolve(deepResearchResult);
            })
            .catch(deepResearchError => {
              console.error('Error executing Deep Research after timeout:', deepResearchError);
              // If Deep Research fails, fall back to the original suggestion
              resolve({
                type: 'tool_error',
                content: `I encountered a timeout while processing your query. ${timeoutGuidance}\n\nI tried to use Deep Research automatically but encountered an error: ${deepResearchError.message}`,
                originalQuery: query
              });
            });
        })
        .catch(ragError => {
          // If the RAG timeout handling itself failed, still execute Deep Research
          console.error('Timeout handling with RAG failed:', ragError);

          // Use a generic timeout message
          const genericTimeoutMessage = 'The operation timed out after 60 seconds. This might be due to a large dataset, complex query, or system load.';

          console.log('RAG query timed out, automatically switching to Deep Research (after RAG guidance failed)');
          mainWindow.webContents.send('log', 'RAG query timed out, automatically switching to Deep Research');

          // Execute Deep Research directly
          executeFullDeepResearch(query, model, {
            resultsCount: userSettings.deepResearchDefaultResultsPerQuery || 5,
            maxSubQueries: userSettings.deepResearchMaxSubQueries || 5,
            urlTimeout: userSettings.deepResearchUrlTimeout || 10000,
            saveToDatabase: false
          })
            .then(deepResearchResult => {
              resolve(deepResearchResult);
            })
            .catch(deepResearchError => {
              console.error('Error executing Deep Research after timeout:', deepResearchError);
              // If Deep Research fails, fall back to the original suggestion
              resolve({
                type: 'tool_error',
                content: `I encountered a timeout while processing your query. ${genericTimeoutMessage}\n\nI tried to use Deep Research automatically but encountered an error: ${deepResearchError.message}`,
                originalQuery: query
              });
            });
        });
    }, 60000); // 60 second timeout (increased from 30 seconds)

    // Add the listeners for the initial relevance check
    mainWindow.webContents.once('rag-relevance-checked', (data) => {
      clearTimeout(timeout);

      // Extract the relevance score and buffer
      const relevanceScore = data.score;
      const buffer = data.buffer;

      console.log(`Relevance check complete. Score: ${relevanceScore}/10`);
      mainWindow.webContents.send('log', `Content relevance score: ${relevanceScore}/10`);

      // If relevance score is below threshold, automatically switch to Deep Research
      if (relevanceScore < 5) {
        console.log(`CONTENT RAG relevance score ${relevanceScore}/10 is below threshold (5). Automatically switching to Deep Research.`);
        mainWindow.webContents.send('log', `Low content relevance score (${relevanceScore}/10). Automatically switching to Deep Research.`);

        // IMPORTANT: Send a message to the UI indicating that we're not showing the RAG results due to low relevance
        mainWindow.webContents.send('rag-partial-answer', `The content knowledge base does not have sufficiently relevant information about this topic (relevance score: ${relevanceScore}/10). Switching to Deep Research to find better information...`);

        // Also send a signal to clear any previous partial answers and show a loading indicator
        mainWindow.webContents.send('rag-answer', `The content knowledge base does not have sufficiently relevant information about this topic (relevance score: ${relevanceScore}/10). Switching to Deep Research to find better information...`);

        // Send a signal to indicate that we're switching tools
        mainWindow.webContents.send('tool-switching', {
          from: 'rag_query',
          to: 'deep_research',
          reason: `Low relevance score (${relevanceScore}/10)`
        });

        // Try to get guidance from the RAG system about using Deep Research
        handleLowRelevanceWithRAG(query, relevanceScore)
          .then(guidance => {
            // Execute Deep Research directly
            console.log('Executing Deep Research due to low RAG relevance');
            executeFullDeepResearch(query, model, {
              resultsCount: userSettings.deepResearchDefaultResultsPerQuery || 5,
              maxSubQueries: userSettings.deepResearchMaxSubQueries || 5,
              urlTimeout: userSettings.deepResearchUrlTimeout || 10000,
              saveToDatabase: false
            })
              .then(deepResearchResult => {
                resolve(deepResearchResult);
              })
              .catch(deepResearchError => {
                console.error('Error executing Deep Research after low RAG relevance:', deepResearchError);

                // Instead of returning RAG results, return a special tool_error type
                resolve({
                  type: 'tool_error',
                  content: `I couldn't find relevant information about this topic. The knowledge base has low relevance (${relevanceScore}/10) and Deep Research encountered an error: ${deepResearchError.message}`,
                  originalQuery: query
                });
              });
          })
          .catch(ragError => {
            // If RAG guidance fails, still execute Deep Research
            console.error('Error getting RAG guidance for low relevance:', ragError);
            executeFullDeepResearch(query, model, {
              resultsCount: userSettings.deepResearchDefaultResultsPerQuery || 5,
              maxSubQueries: userSettings.deepResearchMaxSubQueries || 5,
              urlTimeout: userSettings.deepResearchUrlTimeout || 10000,
              saveToDatabase: false
            })
              .then(deepResearchResult => {
                resolve(deepResearchResult);
              })
              .catch(deepResearchError => {
                console.error('Error executing Deep Research after low RAG relevance:', deepResearchError);

                // Instead of returning RAG results, return a special tool_error type
                resolve({
                  type: 'tool_error',
                  content: `I couldn't find relevant information about this topic. The knowledge base has low relevance (${relevanceScore}/10) and Deep Research encountered an error: ${deepResearchError.message}`,
                  originalQuery: query
                });
              });
          });
      } else if (relevanceScore < 6) {
        // For moderate relevance scores (between 5 and 6), we can proceed with LLM but add a warning
        console.log(`CONTENT RAG relevance score ${relevanceScore}/10 is moderate (between 5 and 6). Proceeding with LLM but will add a warning.`);
        mainWindow.webContents.send('log', `Moderate content relevance score (${relevanceScore}/10). Proceeding with LLM.`);

        // Set up the LLM answer listener
        mainWindow.webContents.once('rag-answer', async (event, answer) => {
          console.log('LLM answer received for moderate relevance score');

          // Add a warning to the answer
          const warningMessage = `NOTE: The content relevance score for this query is moderate (${relevanceScore}/10). The information provided may be partially relevant, but you might want to consider using Deep Research for more comprehensive results.\n\n`;

          // Remove any relevance score from the answer
          answer = answer.replace(/RELEVANCE_SCORE:\s*\d+(\.\d+)?\s*\/\s*10/i, '').trim();

          // Add a delay before validation to avoid rate limiting
          console.log('Adding delay before validation to avoid rate limiting...');
          await new Promise(resolve => setTimeout(resolve, 3000)); // 3 second delay

          // Validate the response with a checkpoint
          validateLLMResponse(query, answer, 'rag_query', relevanceScore)
            .then(validation => {
              // Check if validation failed due to rate limits
              if (checkpointState.errors.some(error =>
                (typeof error === 'string' && error.includes('Rate limit hit, max retries')) ||
                (error && error.message && typeof error.message === 'string' && error.message.includes('Rate limit hit, max retries'))
              )) {
                console.log('Validation failed due to rate limits, but we will still use the answer');
                mainWindow.webContents.send('log', 'Validation failed due to rate limits, but we will still use the answer');

                // Add a note to the answer about the validation failure
                const validationFailureNote = '\n\n[Note: The system was unable to validate this response due to API rate limits. The information provided may still be useful, but has not undergone validation.]';

                // Return the answer with the validation failure note
                resolve({
                  type: 'rag_query',
                  content: warningMessage + answer + validationFailureNote,
                  relevanceScore: relevanceScore
                });
                return;
              }

              if (validation.needsRework) {
                console.log(`Validation checkpoint recommends switching to ${validation.recommendedTool}`);

                // Notify the user about the tool switch
                mainWindow.webContents.send('tool-switching', {
                  from: 'rag_query',
                  to: validation.recommendedTool,
                  reason: `Validation checkpoint (confidence: ${validation.confidence}/10): ${validation.reason}`
                });

                // Execute the recommended tool instead
                if (validation.recommendedTool.toLowerCase().includes('deep research')) {
                  executeFullDeepResearch(query, model, {
                    resultsCount: userSettings.deepResearchDefaultResultsPerQuery || 5,
                    maxSubQueries: userSettings.deepResearchMaxSubQueries || 5,
                    urlTimeout: userSettings.deepResearchUrlTimeout || 10000,
                    saveToDatabase: false
                  })
                    .then(deepResearchResult => {
                      resolve(deepResearchResult);
                    })
                    .catch(deepResearchError => {
                      console.error('Error executing Deep Research after validation checkpoint:', deepResearchError);
                      resolve({
                        type: 'rag_query',
                        content: warningMessage + (validation.enhancedResponse || answer),
                        relevanceScore: relevanceScore
                      });
                    });
                } else {
                  // For other tools, just return the enhanced response
                  resolve({
                    type: 'rag_query',
                    content: warningMessage + (validation.enhancedResponse || answer),
                    relevanceScore: relevanceScore
                  });
                }
              } else {
                // Return the enhanced answer with warning
                resolve({
                  type: 'rag_query',
                  content: warningMessage + (validation.enhancedResponse || answer),
                  relevanceScore: relevanceScore
                });
              }
            })
            .catch(error => {
              console.error('Error validating LLM response:', error);
              // On validation error, return the original answer with warning
              resolve({
                type: 'rag_query',
                content: warningMessage + answer + '\n\n[Note: An error occurred during validation. The information provided may still be useful, but has not undergone validation.]',
                relevanceScore: relevanceScore
              });
            });
        });

        // Now send the query to the LLM
        console.log('Sending query to LLM for moderate relevance score');
        sendToPython(`query:${query}`);

        // Set the LLM mode
        setTimeout(async () => {
          await sendToPython(`use_llm:true`);
          console.log(`Sent explicit LLM mode command: true`);
        }, 1000);
      } else {
        // Relevance score is good, proceed with LLM
        console.log(`CONTENT RAG relevance score ${relevanceScore}/10 is good. Proceeding with LLM.`);
        mainWindow.webContents.send('log', `Good content relevance score (${relevanceScore}/10). Proceeding with LLM.`);

        // Set up the LLM answer listener
        mainWindow.webContents.once('rag-answer', async (event, answer) => {
          console.log('LLM answer received for good relevance score');

          // Remove any relevance score from the answer
          answer = answer.replace(/RELEVANCE_SCORE:\s*\d+(\.\d+)?\s*\/\s*10/i, '').trim();

          // Add a delay before validation to avoid rate limiting
          console.log('Adding delay before validation to avoid rate limiting...');
          await new Promise(resolve => setTimeout(resolve, 3000)); // 3 second delay

          // Validate the response with a checkpoint
          validateLLMResponse(query, answer, 'rag_query', relevanceScore)
            .then(validation => {
              // Check if validation failed due to rate limits
              if (checkpointState.errors.some(error =>
                (typeof error === 'string' && error.includes('Rate limit hit, max retries')) ||
                (error && error.message && typeof error.message === 'string' && error.message.includes('Rate limit hit, max retries'))
              )) {
                console.log('Validation failed due to rate limits, but we will still use the answer');
                mainWindow.webContents.send('log', 'Validation failed due to rate limits, but we will still use the answer');

                // Add a note to the answer about the validation failure
                const validationFailureNote = '\n\n[Note: The system was unable to validate this response due to API rate limits. The information provided may still be useful, but has not undergone validation.]';

                // Return the answer with the validation failure note
                resolve({
                  type: 'rag_query',
                  content: answer + validationFailureNote,
                  relevanceScore: relevanceScore
                });
                return;
              }

              if (validation.needsRework) {
                console.log(`Validation checkpoint recommends switching to ${validation.recommendedTool}`);

                // Notify the user about the tool switch
                mainWindow.webContents.send('tool-switching', {
                  from: 'rag_query',
                  to: validation.recommendedTool,
                  reason: `Validation checkpoint (confidence: ${validation.confidence}/10): ${validation.reason}`
                });

                // Execute the recommended tool instead
                if (validation.recommendedTool.toLowerCase().includes('deep research')) {
                  executeFullDeepResearch(query, model, {
                    resultsCount: userSettings.deepResearchDefaultResultsPerQuery || 5,
                    maxSubQueries: userSettings.deepResearchMaxSubQueries || 5,
                    urlTimeout: userSettings.deepResearchUrlTimeout || 10000,
                    saveToDatabase: false
                  })
                    .then(deepResearchResult => {
                      resolve(deepResearchResult);
                    })
                    .catch(deepResearchError => {
                      console.error('Error executing Deep Research after validation checkpoint:', deepResearchError);
                      resolve({
                        type: 'rag_query',
                        content: validation.enhancedResponse || answer,
                        relevanceScore: relevanceScore
                      });
                    });
                } else {
                  // For other tools, just return the enhanced response
                  resolve({
                    type: 'rag_query',
                    content: validation.enhancedResponse || answer,
                    relevanceScore: relevanceScore
                  });
                }
              } else {
                // Return the enhanced answer
                resolve({
                  type: 'rag_query',
                  content: validation.enhancedResponse || answer,
                  relevanceScore: relevanceScore
                });
              }
            })
            .catch(error => {
              console.error('Error validating LLM response:', error);
              // On validation error, return the original answer with a note
              resolve({
                type: 'rag_query',
                content: answer + '\n\n[Note: An error occurred during validation. The information provided may still be useful, but has not undergone validation.]',
                relevanceScore: relevanceScore
              });
            });
        });

        // Now send the query to the LLM
        console.log('Sending query to LLM for good relevance score');
        sendToPython(`query:${query}`);

        // Set the LLM mode
        setTimeout(async () => {
          await sendToPython(`use_llm:true`);
          console.log(`Sent explicit LLM mode command: true`);
        }, 1000);
      }
    });

    mainWindow.webContents.once('rag-error', (event, error) => {
      clearTimeout(timeout);

      // Use our Python error handler function
      if (error && error.includes && error.includes('Error processing query with embedding model')) {
        handlePythonError(error);
      } else {
        errorListener(event, error);
      }
    });

    // Send the query directly to Python instead of emitting an event
    try {
      console.log('Sending initial retrieval-only CONTENT RAG query to check relevance score');

      // Set the relevance check phase flag
      inRelevanceCheckPhase = true;
      relevanceCheckBuffer = '';

      // Format the query command
      const queryCommand = `query:${query}`;

      // Send the query to Python
      pythonProcess.stdin.write(queryCommand + '\n');
      console.log(`Sent initial CONTENT RAG query to Python: ${queryCommand}`);

      // Force retrieval-only mode for the relevance check
      setTimeout(async () => {
        await sendToPython(`use_llm:false`);
        console.log('Sent explicit retrieval-only mode command for relevance check');
      }, 1000);
    } catch (error) {
      console.error('Error sending query to Python:', error);
      clearTimeout(timeout);

      // Try to get error handling guidance from the RAG system
      handleErrorWithRAG(error.message, query)
        .then(errorGuidance => {
          // Automatically switch to Deep Research
          console.log('Error starting RAG query, automatically switching to Deep Research');
          mainWindow.webContents.send('log', 'Error starting RAG query, automatically switching to Deep Research');

          // Execute Deep Research directly
          executeFullDeepResearch(query, model, {
            resultsCount: userSettings.deepResearchDefaultResultsPerQuery || 5,
            maxSubQueries: userSettings.deepResearchMaxSubQueries || 5,
            urlTimeout: userSettings.deepResearchUrlTimeout || 10000,
            saveToDatabase: false
          })
            .then(deepResearchResult => {
              resolve(deepResearchResult);
            })
            .catch(deepResearchError => {
              console.error('Error executing Deep Research after RAG start error:', deepResearchError);
              // If Deep Research fails, fall back to the original suggestion
              resolve({
                type: 'tool_error',
                content: `I encountered an issue while starting the RAG query. ${errorGuidance}\n\nI tried to use Deep Research automatically but encountered an error: ${deepResearchError.message}`,
                originalQuery: query
              });
            });
        })
        .catch(ragError => {
          // If the RAG error handling itself failed, still try Deep Research
          console.error('Error handling with RAG failed:', ragError);

          // Execute Deep Research directly
          executeFullDeepResearch(query, model, {
            resultsCount: userSettings.deepResearchDefaultResultsPerQuery || 5,
            maxSubQueries: userSettings.deepResearchMaxSubQueries || 5,
            urlTimeout: userSettings.deepResearchUrlTimeout || 10000,
            saveToDatabase: false
          })
            .then(deepResearchResult => {
              resolve(deepResearchResult);
            })
            .catch(deepResearchError => {
              console.error('Error executing Deep Research after RAG start error:', deepResearchError);
              // If Deep Research fails too, then reject with the original error
              reject(new Error(`Error starting RAG query: ${error.message}. Deep Research also failed: ${deepResearchError.message}`));
            });
        });
    }
  });
}

// Helper function to handle errors with RAG
async function handleErrorWithRAG(errorMessage, query) {
  console.log(`Handling error with RAG: "${errorMessage}" for query: "${query}"`);

  try {
    // Create an error summary for the RAG system
    const errorSummary = `Error occurred while executing RAG query: "${query}". Error message: ${errorMessage}`;

    // Query the RAG system for error handling guidance
    console.log('Querying RAG system for error handling guidance');
    const ragResult = await runPythonCommand(['rag_query.py', `error handling for: ${errorSummary}`, 'false', '3']);

    if (ragResult && ragResult.trim()) {
      console.log('RAG system provided error handling guidance');

      // Extract the most relevant guidance from the RAG result
      let guidance = ragResult;

      // If the RAG result is too long, extract the most relevant parts
      if (guidance.length > 500) {
        // Look for specific sections in the RAG result
        const errorSection = guidance.match(/ERROR CATEGORIZATION:([\s\S]*?)(?=RECOVERY ACTIONS:|$)/i);
        const recoverySection = guidance.match(/RECOVERY ACTIONS:([\s\S]*?)(?=ERROR RESPONSE TEMPLATE:|$)/i);
        const templateSection = guidance.match(/ERROR RESPONSE TEMPLATE:([\s\S]*?)(?=DOCUMENT TYPE:|$)/i);

        // Combine the most relevant parts
        guidance = '';
        if (errorSection && errorSection[1]) {
          guidance += errorSection[1].trim() + '\n\n';
        }
        if (recoverySection && recoverySection[1]) {
          guidance += recoverySection[1].trim() + '\n\n';
        }
        if (templateSection && templateSection[1]) {
          guidance += templateSection[1].trim();
        }
      }

      // Clean up the guidance
      guidance = guidance
        .replace(/DOCUMENT TYPE:.*$/im, '')
        .replace(/TITLE:.*$/im, '')
        .replace(/PURPOSE:.*$/im, '')
        .trim();

      // If we still have guidance, use it
      if (guidance) {
        return `${guidance}\n\nSpecific error: ${errorMessage}`;
      }
    }

    // If we couldn't get guidance from the RAG system, provide a generic message
    return `I encountered an error while processing your query. This might be due to a database issue, API error, or processing problem. You could try simplifying your query, checking your API key settings, or trying again later. Specific error: ${errorMessage}`;
  } catch (error) {
    console.error('Error querying RAG system for error handling guidance:', error);
    // Return a generic error message if the RAG query itself fails
    return `I encountered an error while processing your query. Please try again with a simpler query or check your settings. Specific error: ${errorMessage}`;
  }
}

// Helper function to handle low relevance scores with RAG
async function handleLowRelevanceWithRAG(query, relevanceScore) {
  console.log(`Handling low relevance with RAG: "${query}" (score: ${relevanceScore}/10)`);

  try {
    // Create a relevance summary for the RAG system
    const relevanceSummary = `Low relevance score (${relevanceScore}/10) for RAG query: "${query}". Need guidance on suggesting Deep Research instead.`;

    // Query the RAG system for guidance on handling low relevance
    console.log('Querying RAG system for low relevance handling guidance');
    const ragResult = await runPythonCommand(['rag_query.py', `tool selection for: ${relevanceSummary}`, 'false', '3']);

    if (ragResult && ragResult.trim()) {
      console.log('RAG system provided low relevance handling guidance');

      // Extract the most relevant guidance from the RAG result
      let guidance = ragResult;

      // If the RAG result is too long, extract the most relevant parts
      if (guidance.length > 500) {
        // Look for specific sections in the RAG result
        const toolSection = guidance.match(/TOOL SELECTION CRITERIA:([\s\S]*?)(?=DECISION FRAMEWORK:|$)/i);
        const deepResearchSection = guidance.match(/DEEP RESEARCH \(WEB SEARCH\):([\s\S]*?)(?=DOCUMENT ANALYSIS:|$)/i);
        const complexTopicsSection = guidance.match(/COMPLEX TOPICS REQUIRING DEEP RESEARCH:([\s\S]*?)(?=DECISION FRAMEWORK:|$)/i);

        // Combine the most relevant parts
        guidance = '';
        if (deepResearchSection && deepResearchSection[1]) {
          guidance += deepResearchSection[1].trim() + '\n\n';
        }
        if (complexTopicsSection && complexTopicsSection[1]) {
          guidance += complexTopicsSection[1].trim() + '\n\n';
        }
        if (toolSection && toolSection[1]) {
          guidance += toolSection[1].trim();
        }
      }

      // Clean up the guidance
      guidance = guidance
        .replace(/DOCUMENT TYPE:.*$/im, '')
        .replace(/TITLE:.*$/im, '')
        .replace(/PURPOSE:.*$/im, '')
        .trim();

      // If we still have guidance, use it
      if (guidance) {
        return `The knowledge base doesn't have highly relevant information about this topic. ${guidance}`;
      }
    }

    // If we couldn't get guidance from the RAG system, provide a generic message
    return `The knowledge base doesn't have highly relevant information about this topic. Deep Research would be more appropriate as it can search the web for up-to-date and specific information.`;
  } catch (error) {
    console.error('Error querying RAG system for low relevance handling guidance:', error);
    // Return a generic message if the RAG query itself fails
    return `The knowledge base doesn't have highly relevant information about this topic. Deep Research might provide better results by searching the web.`;
  }
}

// Helper function to handle timeouts with RAG
async function handleTimeoutWithRAG(timeoutSummary, query) {
  console.log(`Handling timeout with RAG for query: "${query}"`);

  try {
    // Query the RAG system for timeout handling guidance
    console.log('Querying RAG system for timeout handling guidance');
    const ragResult = await runPythonCommand(['rag_query.py', `timeout handling for: ${timeoutSummary}`, 'false', '3']);

    if (ragResult && ragResult.trim()) {
      console.log('RAG system provided timeout handling guidance');

      // Extract the most relevant guidance from the RAG result
      let guidance = ragResult;

      // If the RAG result is too long, extract the most relevant parts
      if (guidance.length > 500) {
        // Look for specific sections in the RAG result
        const timeoutSection = guidance.match(/TIMEOUT HANDLING:([\s\S]*?)(?=ERROR CATEGORIZATION:|$)/i);
        const recoverySection = guidance.match(/RECOVERY ACTIONS:([\s\S]*?)(?=ERROR RESPONSE TEMPLATE:|$)/i);
        const templateSection = guidance.match(/ERROR RESPONSE TEMPLATE:([\s\S]*?)(?=DOCUMENT TYPE:|$)/i);
        const deepResearchSection = guidance.match(/DEEP RESEARCH RECOMMENDATION:([\s\S]*?)(?=DOCUMENT TYPE:|$)/i);

        // Combine the most relevant parts
        guidance = '';
        if (timeoutSection && timeoutSection[1]) {
          guidance += timeoutSection[1].trim() + '\n\n';
        }
        if (recoverySection && recoverySection[1]) {
          guidance += recoverySection[1].trim() + '\n\n';
        }
        if (deepResearchSection && deepResearchSection[1]) {
          guidance += deepResearchSection[1].trim() + '\n\n';
        }
        if (templateSection && templateSection[1]) {
          guidance += templateSection[1].trim();
        }
      }

      // Clean up the guidance
      guidance = guidance
        .replace(/DOCUMENT TYPE:.*$/im, '')
        .replace(/TITLE:.*$/im, '')
        .replace(/PURPOSE:.*$/im, '')
        .trim();

      // If we still have guidance, use it
      if (guidance) {
        return `${guidance}\n\nThe operation timed out after 30 seconds. Would you like me to try using Deep Research to search the web for information about your query?`;
      }
    }

    // If we couldn't get guidance from the RAG system, provide a generic message with Deep Research suggestion
    return `The operation timed out after 30 seconds. This might be due to a large dataset, complex query, or system load. Would you like me to try using Deep Research to search the web for information about your query?`;
  } catch (error) {
    console.error('Error querying RAG system for timeout handling guidance:', error);
    // Return a generic timeout message if the RAG query itself fails, with Deep Research suggestion
    return `The operation timed out after 30 seconds. Would you like me to try using Deep Research to search the web for information about your query?`;
  }
}

// Helper function to configure Pinecone
async function configurePinecone(apiKey, environment, indexName, usePinecone) {
  console.log(`Configuring Pinecone: indexName=${indexName}, usePinecone=${usePinecone}`);
  mainWindow.webContents.send('log', `Configuring Pinecone: indexName=${indexName}, usePinecone=${usePinecone}`);

  // Update user settings
  userSettings.pineconeApiKey = apiKey;
  userSettings.pineconeEnvironment = environment;
  userSettings.pineconeIndexName = indexName;
  userSettings.usePinecone = usePinecone;

  // Save settings to localStorage
  mainWindow.webContents.executeJavaScript(
    `localStorage.setItem("fungiSettings", '${JSON.stringify(userSettings)}')`
  );

  // Configure Pinecone in the Python backend
  const configCommand = `pinecone_config:${JSON.stringify({
    api_key: apiKey,
    environment: environment,
    index_name: indexName,
    use_pinecone: usePinecone
  })}`;

  await sendToPython(configCommand);

  return {
    type: 'pinecone_config',
    content: `Pinecone configured successfully with index "${indexName}" and usePinecone=${usePinecone}`
  };
}

// Helper function to get model index
function getModelIndex(modelName) {
  const models = [
    'mistralai/mistral-7b-instruct',
    'google/gemini-2.0-flash-001',
    'qwen/qwen3-235b-a22b:free',
    'meta-llama/llama-4-maverick:free',
    'google/gemini-2.0-flash-exp:free'
  ];

  const index = models.indexOf(modelName);
  return index >= 0 ? index : 0; // Return 0-based index, default to 0 if not found
}

// Variables to track answer collection
let collectingAnswer = false;
let answerBuffer = '';
let questionAsked = '';

// Variables to track relevance check phase
let inRelevanceCheckPhase = false;
let relevanceCheckBuffer = '';

// Set up handlers for Python process
function setupPythonProcessHandlers(process) {
  // Buffer for stdout data
  let stdoutBuffer = '';

  // Handle stdout
  process.stdout.on('data', (data) => {
    // Convert buffer to string and add to our buffer
    const chunk = data.toString();
    stdoutBuffer += chunk;

    // Process complete lines
    const lines = stdoutBuffer.split('\n');

    // Keep the last line if it's incomplete (no newline at the end)
    stdoutBuffer = lines.pop() || '';

    // Process each complete line
    for (const line of lines) {
      const output = line.trim();
      if (output) {
        console.log(`Python stdout: ${output}`);

        // Check if we're in the relevance check phase
        if (inRelevanceCheckPhase) {
          // Add this line to the relevance check buffer
          relevanceCheckBuffer += output + '\n';

          // Check if this line contains the relevance score
          if (output.includes('RELEVANCE_SCORE:')) {
            // Extract the relevance score
            const relevanceMatch = output.match(/RELEVANCE_SCORE:\s*(\d+(\.\d+)?)\s*\/\s*10/i);
            if (relevanceMatch && relevanceMatch[1]) {
              const relevanceScore = parseFloat(relevanceMatch[1]);
              console.log(`Detected relevance score during check phase: ${relevanceScore}/10`);

              // We don't need to set this flag anymore since we're emitting the event immediately

              // Emit a special event with the relevance score and the buffer
              mainWindow.webContents.emit('rag-relevance-checked', {
                score: relevanceScore,
                buffer: relevanceCheckBuffer
              });

              // Reset the relevance check phase
              inRelevanceCheckPhase = false;
              relevanceCheckBuffer = '';
            }
          }

          // Don't send anything to the UI during the relevance check phase
          continue;
        }

        // Check if this is the start of an answer
        if (output.startsWith('Answer:') || output.startsWith('A:')) {
          collectingAnswer = true;
          answerBuffer = output + '\n';
          console.log('Started collecting answer');

          // Send this line immediately to show progress
          mainWindow.webContents.send('rag-partial-answer', output);

          // Also send to regular output
          mainWindow.webContents.send('python-output', output);
          continue;
        }

        // If we're collecting an answer, add this line to the buffer
        if (collectingAnswer) {
          // Add this line to the answer buffer
          answerBuffer += output + '\n';

          // Filter out debug messages before sending partial updates
          const debugPatterns = [
            'RELEVANCE_SCORE:',
            'Extracted keywords',
            'Question classified as:',
            'Retrieved',
            'RAG query mode:',
            'Using enhanced retrieval-only mode',
            'Python stdout:',
            'LLM mode explicitly enabled',
            'Batches:',
            'it/s',
            'Ask a question about the website content'
          ];

          const isDebugMessage = debugPatterns.some(pattern => output.includes(pattern));

          if (!isDebugMessage) {
            // Send partial updates for immediate display
            mainWindow.webContents.send('rag-partial-answer', output);
          } else {
            // Log to console but don't send to UI
            console.log('Filtered debug message from partial answer:', output);
          }

          // Check if this is the end of the answer
          if (output.includes('Enter your question') ||
              output.includes('Choose an option:') ||
              output.includes('Select a model:')) {

            // Filter out debug messages from the final answer
            console.log('Sending collected answer:', answerBuffer);

            // Filter out debug lines from the answer
            const debugPatterns = [
              'RELEVANCE_SCORE:',
              'Extracted keywords',
              'Question classified as:',
              'Retrieved',
              'RAG query mode:',
              'Using enhanced retrieval-only mode',
              'Python stdout:',
              'LLM mode explicitly enabled',
              'Batches:',
              'it/s',
              'Ask a question about the website content'
            ];

            // Split the answer into lines and filter out debug lines
            const answerLines = answerBuffer.split('\n');
            const filteredLines = answerLines.filter(line => {
              return !debugPatterns.some(pattern => line.includes(pattern));
            });

            // Join the filtered lines back into a string
            const filteredAnswer = filteredLines.join('\n');

            // Send the filtered answer
            mainWindow.webContents.send('rag-answer', filteredAnswer);

            // Also send a signal to re-enable the input field
            mainWindow.webContents.send('rag-query-complete');

            // Reset collection
            collectingAnswer = false;
            answerBuffer = '';
          }
          // Check if this might be the end of the answer (relevant sources section or empty line)
          else if ((output.includes('RELEVANT SOURCES') ||
                   output.includes('Relevant Sources') ||
                   output.includes('relevant sources')) ||
                   (answerBuffer.length > 100 &&
                   (output === '' || output === '---' || output === '----' || output === '-----'))) {

            // This might be the end of the answer
            // Longer delay to collect any final pieces
            setTimeout(() => {
              if (collectingAnswer && answerBuffer) {
                console.log('Detected end of answer, sending collected answer');

                // Filter out debug lines from the answer
                const debugPatterns = [
                  'RELEVANCE_SCORE:',
                  'Extracted keywords',
                  'Question classified as:',
                  'Retrieved',
                  'RAG query mode:',
                  'Using enhanced retrieval-only mode',
                  'Python stdout:',
                  'LLM mode explicitly enabled',
                  'Batches:',
                  'it/s',
                  'Ask a question about the website content'
                ];

                // Split the answer into lines and filter out debug lines
                const answerLines = answerBuffer.split('\n');
                const filteredLines = answerLines.filter(line => {
                  return !debugPatterns.some(pattern => line.includes(pattern));
                });

                // Join the filtered lines back into a string
                const filteredAnswer = filteredLines.join('\n');

                // Send the filtered answer
                mainWindow.webContents.send('rag-answer', filteredAnswer);

                // Also send a signal to re-enable the input field
                mainWindow.webContents.send('rag-query-complete');

                collectingAnswer = false;
                answerBuffer = '';
              }
            }, 2000); // increased delay to 2 seconds
          }

          // Continue to next line, we've already sent this as a partial answer
          continue;
        }

        // Filter out debug messages before sending to renderer
        const debugPatterns = [
          'RELEVANCE_SCORE:',
          'Extracted keywords',
          'Question classified as:',
          'Retrieved',
          'RAG query mode:',
          'Using enhanced retrieval-only mode',
          'Python stdout:',
          'LLM mode explicitly enabled',
          'Batches:',
          'it/s',
          'Ask a question about the website content'
        ];

        const isDebugMessage = debugPatterns.some(pattern => output.includes(pattern));

        if (!isDebugMessage) {
          // Send to renderer
          mainWindow.webContents.send('python-output', output);
        } else {
          // Log to console but don't send to UI
          console.log('Filtered debug message:', output);
        }

        // Check if Python is waiting for input
        if (output.includes('Enter the starting URL:') ||
            output.includes('Enter maximum number of pages to crawl:') ||
            output.includes('Enter path to ChromeDriver') ||
            output.includes('press Enter to use automatic download') ||
            output.includes('Choose an option:') ||
            output.includes('Enter your question') ||
            output.includes('Select a model:')) {
          mainWindow.webContents.send('python-waiting-input', output);
          console.log('Python is waiting for input: ' + output);

          // If this is a question prompt, save the last question
          if (output.includes('Enter your question')) {
            // If we were collecting an answer, send what we have
            if (collectingAnswer && answerBuffer) {
              console.log('Sending collected answer at prompt:', answerBuffer);
              mainWindow.webContents.send('rag-answer', answerBuffer);
              collectingAnswer = false;
              answerBuffer = '';
            }
          }
        }

        // Check for analysis choice prompt and handle it automatically
        if (handleAnalysisChoice(output)) {
          // Already handled by the function
          console.log('Analysis choice handled automatically');
        }

        // Check for crawl progress
        if (output.includes('Crawled page') && output.includes('of')) {
          try {
            const match = output.match(/Crawled page (\d+) of (\d+)/);
            if (match && match.length >= 3) {
              const current = parseInt(match[1]);
              const total = parseInt(match[2]);
              mainWindow.webContents.send('crawl-progress', { current, total });
            }
          } catch (e) {
            console.error('Error parsing progress:', e);
          }
        }

        // Check for crawl completion
        if (output.includes('Crawl complete') || output.includes('Processing complete')) {
          mainWindow.webContents.send('crawl-complete', true);
        }

        // Check for database initialization
        if (output.includes('Database') &&
            (output.includes('initialized') || output.includes('connected') || output.includes('created'))) {
          mainWindow.webContents.send('database-status', output);
        }
      }
    }
  });

  // Handle stderr
  process.stderr.on('data', (data) => {
    const errorText = data.toString();
    console.error(`Python stderr: ${errorText}`);

    // Check if this is a progress bar output (contains percentage indicators)
    const isProgressBar = errorText.includes('%') &&
                         (errorText.includes('Batches:') ||
                          errorText.includes('it/s') ||
                          errorText.includes('|'));

    if (isProgressBar) {
      // This is likely a progress bar, send as regular output
      mainWindow.webContents.send('python-progress', errorText);

      // Extract percentage if possible
      const percentMatch = errorText.match(/(\d+)%/);
      if (percentMatch && percentMatch[1]) {
        const percent = parseInt(percentMatch[1]);
        if (!isNaN(percent)) {
          mainWindow.webContents.send('rag-progress', percent);
        }
      }
    } else {
      // This is a real error, send as error
      mainWindow.webContents.send('python-error', errorText);

      // Also send to log for visibility
      mainWindow.webContents.send('log', `ERROR: ${errorText}`);
    }
  });

  // Handle process exit
  process.on('close', (code) => {
    console.log(`Python process exited with code ${code}`);
    mainWindow.webContents.send('python-exit', code);

    // Process any remaining buffer
    if (stdoutBuffer.trim()) {
      console.log(`Python stdout (remaining): ${stdoutBuffer.trim()}`);
      mainWindow.webContents.send('python-output', stdoutBuffer.trim());
    }

    // Clear the process reference
    pythonProcess = null;
  });

  // Handle errors
  process.on('error', (error) => {
    console.error(`Python process error: ${error.message}`);
    mainWindow.webContents.send('python-error', error.message);
    mainWindow.webContents.send('log', `Process error: ${error.message}`);
  });
}

// Send input to Python process
async function sendToPython(input) {
  return new Promise((resolve) => {
    if (!pythonProcess) {
      // During startup, we don't want to show errors for expected conditions
      // Just log to console but don't create an error object that will show in the UI
      console.log('Python process is not running yet, command will be skipped:', input);
      // Instead of rejecting, resolve with a default value when Python is not running
      // This prevents errors from breaking the UI flow
      resolve({ success: false, error: 'Python process is not running yet' });
      return;
    }

    if (!pythonProcess.stdin.writable) {
      const error = new Error('Python process stdin is not writable');
      console.error(error);
      // Instead of rejecting, resolve with a default value when stdin is not writable
      resolve({ success: false, error: 'Python process stdin is not writable' });
      return;
    }

    try {
      // Add a newline to the input
      const inputWithNewline = input + '\n';

      // Write to the process stdin
      const success = pythonProcess.stdin.write(inputWithNewline);

      if (success) {
        console.log(`Successfully sent to Python: ${input}`);
        // Add a delay to allow Python to process
        setTimeout(() => resolve({ success: true }), 800);
      } else {
        console.warn('Write buffer is full, waiting for drain event');
        // Wait for the drain event
        pythonProcess.stdin.once('drain', () => {
          console.log(`Sent to Python after drain: ${input}`);
          setTimeout(() => resolve({ success: true }), 800);
        });
      }
    } catch (error) {
      console.error('Error sending input to Python:', error);
      // Instead of rejecting, resolve with a default value when there's an error
      resolve({ success: false, error: error.message });
    }
  });
}

// Handle setting the RAG mode
ipcMain.handle('set-rag-mode', async (event, options = {}) => {
  try {
    // Default to using LLM if not specified
    const useLLM = options.useLLM !== false;

    console.log(`Setting RAG mode: ${useLLM ? 'LLM' : 'retrieval-only'}`);

    // Send the mode change command to Python
    const result = await sendToPython(`use_llm:${useLLM ? 'true' : 'false'}`);

    // Check if sending to Python was successful
    if (!result.success) {
      console.log('Python process not available, cannot set RAG mode');
      // We'll still return a partial success since we tried
      return { success: false, error: result.error, uiContinue: true };
    }

    // Notify the UI
    mainWindow.webContents.send('log', `Set RAG mode to ${useLLM ? 'LLM' : 'retrieval-only'}`);

    // Wait for a response
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Return success
    return { success: true };
  } catch (error) {
    console.error('Error setting RAG mode:', error.message);
    return { success: false, error: error.message, uiContinue: true };
  }
});

// Load settings from localStorage
function loadSettings() {
  try {
    // Try to get settings from the renderer process
    if (mainWindow && mainWindow.webContents) {
      mainWindow.webContents.executeJavaScript('localStorage.getItem("fungiSettings")')
        .then(settings => {
          if (settings) {
            const parsedSettings = JSON.parse(settings);
            Object.assign(userSettings, parsedSettings);
            console.log('Settings loaded from localStorage');

            // Set the API key in the environment variable if available
            if (userSettings.openrouterApiKey) {
              process.env.OPENROUTER_API_KEY = userSettings.openrouterApiKey;
              console.log('API key set from settings');
            }

            // Log Pinecone settings if available
            if (userSettings.pineconeApiKey) {
              console.log('Pinecone settings loaded from localStorage');
              console.log(`Pinecone environment: ${userSettings.pineconeEnvironment}`);
              console.log(`Pinecone index: ${userSettings.pineconeIndexName}`);
              console.log(`Use Pinecone: ${userSettings.usePinecone}`);
            }
          }
        })
        .catch(err => {
          console.error('Error loading settings from localStorage:', err);
        });
    }
  } catch (err) {
    console.error('Error loading settings:', err);
  }
}

// Save settings to localStorage
function saveSettings() {
  try {
    if (mainWindow && mainWindow.webContents) {
      // Convert settings to JSON string
      const settingsJson = JSON.stringify(userSettings);

      // Save to localStorage in the renderer process
      mainWindow.webContents.executeJavaScript(`localStorage.setItem("fungiSettings", '${settingsJson.replace(/'/g, "\\'")}')`)
        .then(() => {
          console.log('Settings saved to localStorage');
        })
        .catch(err => {
          console.error('Error saving settings to localStorage:', err);
        });
    }
  } catch (err) {
    console.error('Error saving settings:', err);
  }
}

// Handle setting the API key
ipcMain.handle('set-api-key', async (event, apiKey) => {
  try {
    console.log(`Setting API key (length: ${apiKey.length})`);

    // Set the API key in the environment variable
    process.env.OPENROUTER_API_KEY = apiKey;

    // Also update our userSettings
    userSettings.openrouterApiKey = apiKey;

    // Check if Python process is running before trying to send the API key
    if (!pythonProcess) {
      console.log('Python process not available, but API key has been set in environment');
      // We'll still return success since the key is set in the environment
      // This allows the app to continue working even if Python is not running
      return { success: true };
    }

    // Send the API key to Python only if the process is running
    const result = await sendToPython(`set_api_key:${apiKey}`);

    // Check if sending to Python was successful
    if (!result.success) {
      console.log('Failed to send API key to Python, but key has been set in environment');
      // We'll still return success since the key is set in the environment
      // This allows the app to continue working even if Python is not running
      return { success: true };
    }

    // Wait for a response
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Return success
    return { success: true };
  } catch (error) {
    console.error('Error setting API key:', error.message);
    return { success: false, error: error.message };
  }
});

// Handle validating the API key
ipcMain.handle('validate-api-key', async (event, apiKey) => {
  try {
    if (!apiKey || apiKey.length < 10) {
      return { valid: false, error: 'API key is too short' };
    }

    // For now, we'll just check if it's a reasonable length
    // In a real implementation, you might want to make a test call to the API
    return { valid: true };
  } catch (error) {
    console.error('Error validating API key:', error.message);
    return { valid: false, error: error.message };
  }
});

// Handle opening external URLs
ipcMain.handle('open-external-url', async (event, url) => {
  try {
    // Open the URL in the default browser
    require('electron').shell.openExternal(url);
    return { success: true };
  } catch (error) {
    console.error('Error opening URL:', error.message);
    return { success: false, error: error.message };
  }
});

// Handle Deep Research mode
ipcMain.handle('deep-research', async (event, data) => {
  try {
    console.log('Starting Deep Research with query:', data.query);
    mainWindow.webContents.send('log', `Starting Deep Research for query: ${data.query}`);

    // Check if we have document context
    if (data.documentContext) {
      console.log(`DOCUMENT MODE ACTIVATED FOR DEEP RESEARCH: Using document context: ${data.documentContext.fileName} (ID: ${data.documentContext.documentId})`);
      mainWindow.webContents.send('log', `DOCUMENT MODE: Analyzing document: ${data.documentContext.fileName}`);

      try {
        // For document analysis, we'll use direct LLM analysis
        // This completely bypasses the RAG system and web search entirely

        // Get the model to use
        const modelIndex = getModelIndex(data.model);

        // Send a special command to analyze the document directly with LLM
        // This will NOT use the RAG system at all
        mainWindow.webContents.send('log', `Sending document directly to LLM for analysis (bypassing RAG system)`);

        // Use our standalone function to handle document analysis
        // This completely bypasses the RAG system
        const command = `analyze_document:${data.documentContext.documentId}:${modelIndex}:${data.query}`;

        // We'll use a simpler approach - create a temporary file with the document content
        // and then analyze it directly with the LLM
        console.log(`Analyzing document ${data.documentContext.documentId} with direct LLM approach (Deep Research mode)`);
        mainWindow.webContents.send('log', `Analyzing document with direct LLM approach (bypassing RAG system)`);

        // First, get the document content
        const { spawn } = require('child_process');
        const getDocProcess = spawn('python', ['main.py', '--get-document', data.documentContext.documentId]);

        let docContent = '';
        let docError = '';

        // Collect output from the process
        getDocProcess.stdout.on('data', (data) => {
          docContent += data.toString();
        });

        // Collect errors from the process
        getDocProcess.stderr.on('data', (data) => {
          docError += data.toString();
          console.error(`Document retrieval error: ${data}`);
        });

        // Wait for the process to complete
        await new Promise((resolve, reject) => {
          getDocProcess.on('close', (code) => {
            if (code === 0) {
              resolve();
            } else {
              reject(new Error(`Document retrieval process exited with code ${code}: ${docError}`));
            }
          });
        });

        // Extract the document content
        let extractedContent = '';
        if (docContent.includes('document_content:')) {
          extractedContent = docContent.replace('document_content:', '');
        } else {
          throw new Error('Failed to retrieve document content');
        }

        console.log(`Retrieved document content (${extractedContent.length} characters)`);
        mainWindow.webContents.send('log', `Retrieved document content (${extractedContent.length} characters)`);

        // Now, analyze the document with the LLM
        // Create a prompt for the LLM
        const prompt = `I need you to perform a deep analysis of the following document:

DOCUMENT: ${data.documentContext.fileName}

USER QUESTION: ${data.query}

DOCUMENT CONTENT:
${extractedContent}

Please provide a comprehensive analysis based ONLY on the information in this document.
Your analysis should include:

1. A direct answer to the user's question
2. Key insights from the document related to the question
3. Important details, facts, or figures from the document
4. Any limitations or gaps in the document regarding the question

If the document doesn't contain information to answer the question, clearly state that.
Do not make up information or use your general knowledge to fill in gaps.
Focus specifically on addressing the user's question using only the document content provided.

Start your answer with "Based on the document:" and include relevant quotes or sections from the document to support your analysis.
`;

        // Create a system message for the LLM
        const systemMessage = (
          "You are a document analysis assistant specializing in deep research. Your task is to analyze documents thoroughly and provide comprehensive answers. "
          + "Only use information contained in the document provided. "
          + "If the document doesn't contain the information needed to answer the question, clearly state that. "
          + "Do not make up information or use your general knowledge to fill in gaps. "
          + "Be precise, thorough, and analytical in your response."
        );

        // Call the LLM directly using OpenRouter
        console.log('Calling LLM directly with document content (Deep Research mode)');
        mainWindow.webContents.send('log', 'Calling LLM directly with document content');

        // Get the API key from settings
        // First check if we have it in the environment
        let apiKey = process.env.OPENROUTER_API_KEY;

        // If not, try to get it from the stored settings
        if (!apiKey) {
          try {
            // Get the stored API key
            apiKey = userSettings.openrouterApiKey;
            if (apiKey) {
              console.log('Retrieved API key from stored settings');
            }
          } catch (err) {
            console.error('Error retrieving API key from stored settings:', err);
          }
        }

        // If we still don't have an API key, throw an error
        if (!apiKey) {
          throw new Error('OpenRouter API key not set. Please set up your API key in settings.');
        }

        console.log(`Using API key (first 4 chars): ${apiKey.substring(0, 4)}...`);
        mainWindow.webContents.send('log', 'Using API key from settings');

        // Call the OpenRouter API directly using dynamic import
        const { default: fetch } = await import('node-fetch');
        const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`,
            'HTTP-Referer': 'http://localhost:3000',
            'X-Title': 'Fungi Deep Document Analysis'
          },
          body: JSON.stringify({
            model: data.model || 'mistralai/mistral-7b-instruct',
            messages: [
              { role: 'system', content: systemMessage },
              { role: 'user', content: prompt }
            ]
          })
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`OpenRouter API error: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const responseData = await response.json();
        const llmResponse = responseData.choices[0].message.content;

        console.log('LLM response received successfully (Deep Research mode)');
        mainWindow.webContents.send('log', 'LLM response received successfully');

        // Add a footer with document information
        const finalResponse = `${llmResponse}\n\nDocument: ${data.documentContext.fileName}`;

        // Create a placeholder source for the document
        const documentSource = {
          title: `Document: ${data.documentContext.fileName}`,
          url: `document://${data.documentContext.documentId}`,
          snippet: `Content from document: ${data.documentContext.fileName}`
        };

        // Send the analysis to the renderer
        console.log('Sending Deep Research results to renderer');
        mainWindow.webContents.send('deep-research-complete', {
          query: data.query,
          analysis: finalResponse,
          sources: [documentSource]
        });

        // Also send a signal to re-enable the input field
        mainWindow.webContents.send('rag-query-complete');

        // We've sent the command, so we don't need to do web search
        return { success: true };
      } catch (error) {
        console.error('Error analyzing document in Deep Research mode:', error);
        mainWindow.webContents.send('log', `Error analyzing document: ${error.message}`);

        // Send error to renderer
        console.log('Sending error to renderer');
        mainWindow.webContents.send('deep-research-error', {
          error: error.message
        });

        // Also send a signal to re-enable the input field
        mainWindow.webContents.send('rag-query-complete');

        return { success: false, error: error.message };
      }
    }

    // If no document context, perform multi-query web search
    mainWindow.webContents.send('log', `Starting multi-query research for: ${data.query}`);

    // Log the multi-query parameters
    const maxSubQueries = data.maxSubQueries || userSettings.deepResearchMaxSubQueries || 5;
    const urlTimeout = data.urlTimeout || userSettings.deepResearchUrlTimeout || 10000;
    mainWindow.webContents.send('log', `Multi-query settings: ${maxSubQueries} max sub-queries, ${urlTimeout/1000}s URL timeout`);

    // STEP 1: Generate sub-queries using LLM
    const subQueries = await generateSubQueries(data.query, maxSubQueries, data.model);

    if (!subQueries || subQueries.length === 0) {
      // Fallback to single query if sub-query generation fails
      mainWindow.webContents.send('log', 'Sub-query generation failed, falling back to single query');
      const searchResults = await performWebSearch(data.query, data.resultsCount);
      return await processSingleQueryResults(data, searchResults);
    }

    mainWindow.webContents.send('log', `Generated ${subQueries.length} focused search queries`);

    // STEP 2: Perform parallel searches for all sub-queries
    const allSearchResults = [];
    const allExtractedContents = [];

    for (let i = 0; i < subQueries.length; i++) {
      const subQuery = subQueries[i];
      mainWindow.webContents.send('log', `Searching query ${i + 1}/${subQueries.length}: "${subQuery}"`);

      try {
        const searchResults = await performWebSearch(subQuery, data.resultsCount);
        if (searchResults && searchResults.length > 0) {
          // Extract content from search results with timeout handling
          const extractedContents = await extractContentWithTimeout(searchResults, subQuery, i + 1, subQueries.length, data.urlTimeout);

          allSearchResults.push(...searchResults);
          allExtractedContents.push(...extractedContents);
        }
      } catch (error) {
        console.warn(`Error searching sub-query "${subQuery}":`, error.message);
        mainWindow.webContents.send('log', `Warning: Search failed for query ${i + 1}: ${error.message}`);
      }
    }

    // Remove duplicates based on URL
    const uniqueResults = removeDuplicateResults(allSearchResults, allExtractedContents);
    const finalSearchResults = uniqueResults.searchResults;
    const finalExtractedContents = uniqueResults.extractedContents;

    mainWindow.webContents.send('log', `Collected ${finalSearchResults.length} unique sources from ${subQueries.length} searches`);

    if (!finalSearchResults || finalSearchResults.length === 0) {
      throw new Error('No search results found for this query');
    }

    // STEP 3: Skip gap analysis to reduce API calls and avoid rate limiting
    console.log('Skipping gap analysis to reduce API calls and avoid rate limiting');
    mainWindow.webContents.send('log', 'Skipping gap analysis to reduce API calls');

    // TODO: Re-enable gap analysis once rate limiting is resolved
    // const gapAnalysis = await analyzeContentGaps(data.query, finalExtractedContents, data.model);

    if (false) { // Disabled gap analysis
      const remainingQueries = maxSubQueries - subQueries.length;
      const followUpQueries = []; // gapAnalysis.additionalQueries.slice(0, remainingQueries);

      if (followUpQueries.length > 0) {
        mainWindow.webContents.send('log', `Analyzing gaps, adding ${followUpQueries.length} follow-up queries`);

        // Perform follow-up searches
        for (let i = 0; i < followUpQueries.length; i++) {
          const followUpQuery = followUpQueries[i];
          mainWindow.webContents.send('log', `Follow-up query ${i + 1}/${followUpQueries.length}: "${followUpQuery}"`);

          try {
            const searchResults = await performWebSearch(followUpQuery, data.resultsCount);
            if (searchResults && searchResults.length > 0) {
              const extractedContents = await extractContentWithTimeout(searchResults, followUpQuery, i + 1, followUpQueries.length, data.urlTimeout);

              // Add to final results (with duplicate removal)
              const newUniqueResults = removeDuplicateResults([...finalSearchResults, ...searchResults], [...finalExtractedContents, ...extractedContents]);
              finalSearchResults.length = 0;
              finalExtractedContents.length = 0;
              finalSearchResults.push(...newUniqueResults.searchResults);
              finalExtractedContents.push(...newUniqueResults.extractedContents);
            }
          } catch (error) {
            console.warn(`Error in follow-up search "${followUpQuery}":`, error.message);
            mainWindow.webContents.send('log', `Warning: Follow-up search failed: ${error.message}`);
          }
        }

        mainWindow.webContents.send('log', `Final collection: ${finalSearchResults.length} unique sources`);
      }
    }

    // STEP 4: Sequential analysis with progressive summarization (same as executeFullDeepResearch)
    console.log(`Starting sequential analysis of ${finalExtractedContents.length} sources`);
    mainWindow.webContents.send('log', `Analyzing ${finalExtractedContents.length} sources individually...`);

    const individualSummaries = [];

    // Analyze each source individually
    for (let i = 0; i < finalExtractedContents.length; i++) {
      const content = finalExtractedContents[i];
      const sourceNumber = i + 1;

      console.log(`Analyzing source ${sourceNumber}/${finalExtractedContents.length}: ${content.title}`);
      mainWindow.webContents.send('log', `Analyzing source ${sourceNumber}/${finalExtractedContents.length}: ${content.title}`);

      try {
        const individualAnalysis = await analyzeIndividualSource(content, data.query, data.model, sourceNumber, finalExtractedContents.length);

        if (individualAnalysis && individualAnalysis.trim()) {
          individualSummaries.push({
            sourceNumber: sourceNumber,
            title: content.title,
            url: content.url,
            summary: individualAnalysis
          });

          console.log(`✓ Source ${sourceNumber} analyzed successfully`);
          mainWindow.webContents.send('log', `✓ Source ${sourceNumber} analyzed successfully`);
        } else {
          console.warn(`⚠ Source ${sourceNumber} analysis returned empty result`);
          mainWindow.webContents.send('log', `⚠ Source ${sourceNumber} analysis was empty`);
        }

        // Small delay between analyses to avoid rate limiting
        if (i < finalExtractedContents.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 3000)); // 3 second delay
        }

      } catch (error) {
        console.error(`Error analyzing source ${sourceNumber}:`, error.message);
        mainWindow.webContents.send('log', `Error analyzing source ${sourceNumber}: ${error.message}`);

        // Continue with other sources even if one fails
        continue;
      }
    }

    console.log(`Individual analysis complete. ${individualSummaries.length}/${finalExtractedContents.length} sources analyzed successfully`);
    mainWindow.webContents.send('log', `Individual analysis complete. ${individualSummaries.length} sources analyzed successfully`);

    if (individualSummaries.length === 0) {
      throw new Error('No sources could be analyzed successfully');
    }

    // STEP 5: Final synthesis of all summaries
    console.log('Starting final synthesis of all summaries...');
    mainWindow.webContents.send('log', 'Synthesizing all source analyses...');

    const analysis = await synthesizeAllSummaries(individualSummaries, data.query, data.model);

    // Use the synthesized analysis and prepare for response
    const searchResults = finalSearchResults;
    const extractedContents = finalExtractedContents;

    console.log('Sequential analysis and synthesis completed successfully');
    mainWindow.webContents.send('log', 'Sequential analysis and synthesis completed successfully');

    // Use the synthesized analysis from sequential processing
    console.log('Using synthesized analysis from sequential processing');
    mainWindow.webContents.send('log', 'Using synthesized analysis from sequential processing');

    // The analysis is already complete from the sequential processing above
    const initialAnalysis = analysis;

    // Check if the analysis suggests additional search queries
    if (initialAnalysis.includes('ADDITIONAL SEARCH QUERIES')) {
      mainWindow.webContents.send('log', 'Initial analysis suggests additional search queries. Performing follow-up searches...');

      // Extract the additional search queries
      const additionalQueriesSection = initialAnalysis.split('ADDITIONAL SEARCH QUERIES')[1].split('RELEVANT SOURCES')[0].trim();
      const additionalQueries = additionalQueriesSection
        .split('\n')
        .filter(line => line.trim().length > 0)
        .map(line => line.replace(/^\d+\.\s*/, '').trim()) // Remove numbering
        .filter(query => query.length > 0);

      if (additionalQueries.length > 0) {
        mainWindow.webContents.send('log', `Found ${additionalQueries.length} additional search queries to explore`);

        // Perform additional searches and extract content
        const additionalContents = [];

        for (let i = 0; i < Math.min(additionalQueries.length, 2); i++) { // Limit to 2 additional queries
          const query = additionalQueries[i];
          mainWindow.webContents.send('log', `Performing additional search for: ${query}`);

          try {
            // Search for additional information
            const additionalResults = await performWebSearch(query, 2); // Get 2 results per query

            if (additionalResults && additionalResults.length > 0) {
              // Extract content from each result
              for (let j = 0; j < additionalResults.length; j++) {
                const result = additionalResults[j];
                mainWindow.webContents.send('log', `Extracting content from additional source ${j+1}/${additionalResults.length}: ${result.title}`);

                try {
                  const content = await extractContentFromUrl(result.url);
                  additionalContents.push(content);
                } catch (error) {
                  console.warn(`Error extracting content from additional source ${result.url}:`, error.message);
                }
              }
            }
          } catch (error) {
            console.warn(`Error performing additional search for "${query}":`, error.message);
          }
        }

        // If we found additional content, perform a second analysis
        if (additionalContents.length > 0) {
          mainWindow.webContents.send('log', `Found ${additionalContents.length} additional sources. Performing comprehensive analysis...`);

          // Combine all contents
          const allContents = [...extractedContents, ...additionalContents];

          // Prepare content for final analysis
          let finalCombinedContent = '';
          allContents.forEach((item, index) => {
            const truncatedContent = item.content.length > maxContentLength
              ? item.content.substring(0, maxContentLength) + '... [content truncated due to length]'
              : item.content;

            finalCombinedContent += `\n\nSOURCE ${index + 1}: ${item.url}\nTITLE: ${item.title}\n\n${truncatedContent}\n`;
          });

          // Try to get dynamic instructions from the RAG system for the final analysis
          console.log('Retrieving dynamic instructions for final analysis...');
          mainWindow.webContents.send('log', 'Retrieving dynamic instructions for final analysis...');

          // Reuse the dynamic instructions from before if available, or try to get them again
          if (!dynamicInstructions) {
            try {
              // Query the RAG system for deep research instructions
              const ragResult = await runPythonCommand(['rag_query.py', 'deep research instructions', 'false', '3']);

              if (ragResult && ragResult.includes('deep_research_instructions')) {
                console.log('Found deep research instructions in RAG system for final analysis');

                // Extract the instructions from the RAG response
                const instructionsMatch = ragResult.match(/INSTRUCTIONS FOR DEEP RESEARCH MODE:([\s\S]*?)(?=DOCUMENT TYPE:|$)/i);
                if (instructionsMatch && instructionsMatch[1]) {
                  dynamicInstructions = instructionsMatch[1].trim();
                  console.log('Extracted dynamic instructions for final analysis');
                }
              }
            } catch (error) {
              console.warn('Error retrieving dynamic instructions for final analysis:', error);
              // Continue with hardcoded instructions if RAG query fails
            }
          }

          // Create final analysis prompt with dynamic or fallback instructions
          const finalPrompt = `You are a research assistant analyzing web search results about: "${data.query}"

I've collected information from ${allContents.length} sources, including initial search results and follow-up searches:
${allContents.map((item, index) => `- ${item.title} (${item.url})`).join('\n')}

Below is the content from these sources:

${finalCombinedContent}

${dynamicInstructions ? dynamicInstructions : `Your task is to:
1. Analyze all the provided information thoroughly
2. Extract key insights, examples, and use cases related to the query
3. Synthesize a comprehensive answer that directly addresses the query
4. Cite specific sources when providing information
5. If the sources contain conflicting information, acknowledge this and present different perspectives

IMPORTANT INSTRUCTIONS:
- Base your analysis ONLY on the content provided in these sources, not on your prior knowledge
- Do NOT make up information that isn't in the sources
- If the sources don't contain relevant information to answer the question, state this clearly
- Include ALL relevant information from the sources
- Format your response with clear sections and headings
- Start your answer with "Answer:" and end with a "RELEVANT SOURCES" section listing the URLs
- IGNORE any gibberish, corrupted text, or irrelevant content in the sources
- Focus on extracting meaningful, coherent information only
- Ensure your response is well-structured, coherent, and directly addresses the query
- Use bullet points, numbered lists, and clear headings to organize information
- If you encounter any nonsensical text, simply ignore it and focus on the valid content`}

For this specific query about "${data.query}", focus on:
- Identifying specific, real-world applications and examples
- Categorizing information by domain or use case
- Highlighting the benefits and impact in each scenario
- Noting any innovative or unique implementations
- Extracting practical insights that directly answer the query
- Providing a comprehensive, well-structured answer that is easy to understand

Since we've already performed follow-up searches based on your initial analysis, please provide a COMPREHENSIVE final answer that synthesizes ALL the information from both initial and follow-up searches.

Provide a well-structured response with clear sections and examples.`;

          // Perform final analysis
          const finalAnalysis = await analyzeWithLLM(finalPrompt, tempFilePath, allContents, data.query, data.model);

          // Use the final analysis as the result
          mainWindow.webContents.send('log', 'Comprehensive analysis complete with additional sources');

          // We no longer save research data to the database to avoid using cached results
          // This ensures that each Deep Research query uses fresh data from the web
          mainWindow.webContents.send('log', 'Using fresh data for analysis (not saving to database)');

          // Log that we're not saving to database to ensure fresh results each time
          console.log('Deep Research mode: Not saving to database to ensure fresh results each time');

          // Send the analysis to the renderer
          try {
            // Ensure the analysis is a valid string
            const safeAnalysis = typeof finalAnalysis === 'string' ? finalAnalysis :
              'Answer: I encountered an issue processing the research results. Please try again with a more specific query.';

            // Ensure we have valid sources
            const safeSources = [
              ...Array.isArray(searchResults) ? searchResults.map(source => ({
                title: source.title || 'Unknown Source',
                url: source.url || '#',
                snippet: typeof source.snippet === 'string' ? source.snippet : ''
              })) : [],
              ...Array.isArray(additionalContents) ? additionalContents.map(c => ({
                title: c.title || 'Additional Source',
                url: c.url || '#',
                snippet: typeof c.content === 'string' ? c.content.substring(0, 150) + '...' : ''
              })) : []
            ];

            // Send the data to the renderer with error handling
            console.log('Sending Deep Research results to renderer');
            mainWindow.webContents.send('deep-research-complete', {
              query: data.query,
              analysis: safeAnalysis,
              sources: safeSources
            });
          } catch (renderError) {
            console.error('Error sending Deep Research results to renderer:', renderError);
            // Send a simplified error response
            mainWindow.webContents.send('deep-research-complete', {
              query: data.query,
              analysis: 'Answer: An error occurred while processing the research results. Please try again.',
              sources: []
            });
          }

          // Also send a signal to re-enable the input field
          mainWindow.webContents.send('rag-query-complete');

          return { success: true };
        }
      }
    }

    // If no additional queries were found or no additional content was extracted,
    // use the initial analysis
    mainWindow.webContents.send('log', 'Analysis complete');

    // We no longer save research data to the database to avoid using cached results
    // This ensures that each Deep Research query uses fresh data from the web
    mainWindow.webContents.send('log', 'Using fresh data for analysis (not saving to database)');

    // Log that we're not saving to database to ensure fresh results each time
    console.log('Deep Research mode: Not saving to database to ensure fresh results each time');

    // For intelligent mode, return the result in the correct format
    console.log('Returning sequential analysis result for intelligent mode');
    console.log(`Final result.result.content (first 200 chars): ${initialAnalysis ? initialAnalysis.substring(0, 200) : 'null/undefined'}`);
    console.log('=== END SENDING FINAL RESULT TO UI ===');

    // Return the result in the format expected by intelligent mode
    return {
      type: 'deep_research',
      content: initialAnalysis || 'Answer: I encountered an issue processing the research results. Please try again with a more specific query.',
      sources: searchResults
    };
  } catch (error) {
    console.error('Error in Deep Research:', error.message);
    mainWindow.webContents.send('log', `Error in Deep Research: ${error.message}`);

    // Send error to renderer
    mainWindow.webContents.send('deep-research-error', {
      error: error.message
    });

    // Also send a signal to re-enable the input field
    // This is a backup in case the deep-research-error handler fails
    mainWindow.webContents.send('rag-query-complete');

    // Return an error object instead of throwing
    return {
      success: false,
      error: error.message
    };
  }
});

// Helper function to analyze content with LLM
async function analyzeWithLLM(prompt, tempFilePath, extractedContents, query, model) {
  try {
    // Write the prompt to a temporary file
    const fs = require('fs');
    fs.writeFileSync(tempFilePath, prompt);
    mainWindow.webContents.send('log', 'Prompt saved to temporary file');

    // Get the model index for the Python script
    const modelIndex = getModelIndex(model);
    console.log(`Using model: ${model} (index: ${modelIndex}) for LLM analysis`);
    mainWindow.webContents.send('log', `Using model: ${model} for LLM analysis`);

    // Start a new Python process specifically for this analysis
    const { spawn } = require('child_process');
    const analysisProcess = spawn('python', ['main.py', '--direct-analysis', tempFilePath, modelIndex.toString()]);

    let analysisOutput = '';
    let analysisError = '';

    // Collect output from the analysis process
    analysisProcess.stdout.on('data', (data) => {
      const output = data.toString();
      analysisOutput += output;

      // Check if this is a debug line
      if (output.includes('DEBUG:')) {
        console.log(`LLM Debug: ${output.trim()}`);
        // Don't send debug lines to the UI
      } else {
        // Only send non-debug output to the UI
        mainWindow.webContents.send('log', `Analysis progress: ${output.substring(0, 100)}...`);
      }
    });

    // Collect errors from the analysis process
    analysisProcess.stderr.on('data', (data) => {
      analysisError += data.toString();
      console.error(`Analysis error: ${data}`);
    });

    // Wait for the analysis process to complete
    const analysisResult = await new Promise((resolve, reject) => {
      analysisProcess.on('close', (code) => {
        if (code === 0) {
          console.log(`Analysis process completed successfully with output length: ${analysisOutput.length} characters`);
          resolve(analysisOutput);
        } else {
          console.error(`Analysis process exited with code ${code}`);
          console.error(`Error details: ${analysisError}`);
          reject(new Error(`Analysis process exited with code ${code}: ${analysisError}`));
        }
      });

      // Set a timeout to prevent hanging - reduced to 60 seconds for faster response
      setTimeout(() => {
        console.error('Analysis process timed out after 60 seconds');

        // Check if we have any output before killing the process
        if (analysisOutput && analysisOutput.length > 0) {
          console.log(`Process timed out, but we have ${analysisOutput.length} characters of output. Using partial results.`);

          // Create a custom error with the partial output
          const timeoutError = new Error('Analysis process timed out after 60 seconds');
          timeoutError.partialOutput = analysisOutput;

          // Kill the process
          analysisProcess.kill();

          // Reject with the custom error that includes the partial output
          reject(timeoutError);
        } else {
          // No output, just kill the process and reject
          analysisProcess.kill();
          reject(new Error('Analysis process timed out after 60 seconds with no output'));
        }
      }, 60000); // 1 minute
    });

    // Clean up the temporary file
    fs.unlinkSync(tempFilePath);

    // Log the raw output for debugging
    console.log('='.repeat(40));
    console.log('RAW ANALYSIS OUTPUT BEGIN');
    console.log('='.repeat(40));
    console.log(analysisOutput.substring(0, 500) + '...');
    console.log('...output truncated for log...');
    console.log(analysisOutput.substring(analysisOutput.length - 500));
    console.log('='.repeat(40));
    console.log('RAW ANALYSIS OUTPUT END');
    console.log('='.repeat(40));

    // Process the analysis output
    let result;

    // DEBUG: Log the raw output to see what we're getting
    console.log('DEBUG: RAW ANALYSIS OUTPUT SAMPLE:');
    console.log('='.repeat(50));
    console.log(analysisOutput.substring(0, 1000));
    console.log('...');
    console.log(analysisOutput.substring(analysisOutput.length - 1000));
    console.log('='.repeat(50));
    console.log(`DEBUG: Total output length: ${analysisOutput.length} characters`);
    console.log(`DEBUG: Output contains "Answer:": ${analysisOutput.includes('Answer:')}`);
    console.log(`DEBUG: Output contains "RELEVANT SOURCES": ${analysisOutput.includes('RELEVANT SOURCES')}`);

    // Check if the output contains an answer section
    if (analysisOutput.includes('Answer:')) {
      console.log('Found "Answer:" marker in the output');

      // Extract everything from "Answer:" onwards
      const answerIndex = analysisOutput.indexOf('Answer:');
      console.log(`"Answer:" found at position ${answerIndex}`);
      result = analysisOutput.substring(answerIndex);
      console.log(`Extracted result starting with: ${result.substring(0, 100)}...`);

      // Clean up the result by removing any Python debug output or other noise
      const lines = result.split('\n');
      console.log(`Split result into ${lines.length} lines`);
      const cleanedLines = [];
      let inAnswer = false;
      let debugLinesRemoved = 0;

      // When cleaning content, increase the limit
      function cleanContent(text) {
        if (!text) return '';

        // Remove extra whitespace and normalize line breaks
        return text
          .replace(/\\s+/g, ' ')
          .replace(/\\n+/g, '\\n')
          .trim(); // Remove the substring limit
      }

      for (const line of lines) {
        const trimmedLine = line.trim();
        if (trimmedLine.startsWith('Answer:')) {
          inAnswer = true;
        }
        if (inAnswer) {
          if (trimmedLine.startsWith('DEBUG:')) {
            debugLinesRemoved++;
            continue;
          }
          cleanedLines.push(cleanContent(trimmedLine));
        }
      }

      result = cleanedLines.join('\n');
      console.log(`Cleaned result: ${result.substring(0, 100)}...`);
      console.log(`Debug lines removed: ${debugLinesRemoved}`);
    } else {
      console.log('No "Answer:" marker found in the output');

      // DEBUG: If no Answer marker, check for other patterns
      console.log('DEBUG: Checking for alternative response patterns');

      // Check if the output contains any meaningful text
      if (analysisOutput.trim().length > 100) {
        console.log('DEBUG: Output contains substantial text, adding "Answer:" prefix');
        result = "Answer: " + analysisOutput.trim();
      } else {
        console.log('DEBUG: Output appears to be empty or corrupted');
        result = "Answer: I couldn't generate a proper response from the sources. The content extraction process may have failed to retrieve meaningful information about your query. Please try rephrasing your question or using a different search approach.";
      }
    }

    // DEBUG: Check if the result contains the required sections
    console.log(`DEBUG: Final result contains "Answer:": ${result.includes('Answer:')}`);
    console.log(`DEBUG: Final result contains "RELEVANT SOURCES": ${result.includes('RELEVANT SOURCES')}`);
    console.log(`DEBUG: Final result length: ${result.length} characters`);

    // Return the processed result
    return result;
  } catch (error) {
    console.error('Error analyzing content with LLM:', error.message);
    if (error.partialOutput) {
      console.log('Returning partial output due to timeout:', error.partialOutput.substring(0, 100) + '...');
      return error.partialOutput;
    }
    throw error;
  }
}

// Helper functions for Deep Research mode

// Handle the response from the Deep Research process
async function handleDeepResearchResponse(response, searchResults) {
  try {
    // Check if the response indicates insufficient information
    if (response.includes('INSUFFICIENT_INFORMATION:')) {
      // Extract the message
      const insufficientMatch = response.match(/INSUFFICIENT_INFORMATION:\s*(.*?)(?=\n|$)/);
      const insufficientMessage = insufficientMatch ? insufficientMatch[1].trim() : 'I don\'t have enough data to properly answer this query.';

      // Check if there's a suggested tool
      let suggestedTool = null;
      const toolMatch = response.match(/SUGGESTED_TOOL:\s*([A-Za-z\s]+)(?:\s*-\s*(.*))?/);
      if (toolMatch && toolMatch[1]) {
        suggestedTool = {
          tool: toolMatch[1].trim(),
          reason: toolMatch[2] ? toolMatch[2].trim() : 'This tool might be more appropriate for your query.'
        };

        // Store this suggestion in the RAG system for future reference
        try {
          // Create a document with the tool suggestion
          const suggestionDoc = `Query: "${response}"\nDetected tool: Deep Research\nLLM response: ${response}\nSUGGESTED_TOOL: ${suggestedTool.tool} - ${suggestedTool.reason}`;

          // Save to a temporary file
          const fs = require('fs');
          const os = require('os');
          const path = require('path');
          const tempFile = path.join(os.tmpdir(), 'tool_suggestion.txt');
          fs.writeFileSync(tempFile, suggestionDoc);

          // Add to the RAG system
          console.log('Storing tool suggestion in RAG system');
          try {
            const addResult = await runPythonCommand(['add_document.py', tempFile, 'tool_suggestion', 'Tool suggestion for insufficient data']);
            console.log('Tool suggestion stored in RAG system:', addResult);
          } catch (pythonError) {
            console.error('Error running Python command:', pythonError);
            // Continue even if Python command fails
          }

          // Clean up
          fs.unlinkSync(tempFile);
        } catch (error) {
          console.error('Error storing tool suggestion in RAG system:', error);
          // Continue even if storing fails
        }

        // Format the response with the suggestion
        return `🧠 Research Results\n${insufficientMessage}\n\nI suggest using the ${suggestedTool.tool} tool instead: ${suggestedTool.reason}\n\nSources attempted:\n${searchResults.map(result => result.title).join('\n')}`;
      }

      // Format the response for insufficient information without a suggestion
      return `🧠 Research Results\n${insufficientMessage}\n\nSources attempted:\n${searchResults.map(result => result.title).join('\n')}`;
    }

    // Check if there's a suggested tool without the insufficient information marker
    const toolMatch = response.match(/SUGGESTED_TOOL:\s*([A-Za-z\s]+)(?:\s*-\s*(.*))?/);
    if (toolMatch && toolMatch[1]) {
      const suggestedTool = {
        tool: toolMatch[1].trim(),
        reason: toolMatch[2] ? toolMatch[2].trim() : 'This tool might be more appropriate for your query.'
      };

      // Store this suggestion in the RAG system
      try {
        // Create a document with the tool suggestion
        const suggestionDoc = `Query: "${response}"\nDetected tool: Deep Research\nLLM response: ${response}\nSUGGESTED_TOOL: ${suggestedTool.tool} - ${suggestedTool.reason}`;

        // Save to a temporary file
        const fs = require('fs');
        const os = require('os');
        const path = require('path');
        const tempFile = path.join(os.tmpdir(), 'tool_suggestion.txt');
        fs.writeFileSync(tempFile, suggestionDoc);

        // Add to the RAG system
        console.log('Storing tool suggestion in RAG system');
        try {
          const addResult = await runPythonCommand(['add_document.py', tempFile, 'tool_suggestion', 'Tool suggestion from response']);
          console.log('Tool suggestion stored in RAG system:', addResult);
        } catch (pythonError) {
          console.error('Error running Python command:', pythonError);
          // Continue even if Python command fails
        }

        // Clean up
        fs.unlinkSync(tempFile);
      } catch (error) {
        console.error('Error storing tool suggestion in RAG system:', error);
        // Continue even if storing fails
      }

      // Remove the SUGGESTED_TOOL line from the response
      const cleanedResponse = response.replace(/SUGGESTED_TOOL:\s*([A-Za-z\s]+)(?:\s*-\s*(.*))?(\n|$)/, '');

      // Format the response with the suggestion
      return `🧠 Research Results\n${cleanedResponse}\n\nNote: The ${suggestedTool.tool} tool might also be helpful: ${suggestedTool.reason}\n\nSources:\n${searchResults.map(result => result.title).join('\n')}`;
    }

    // Enhanced response formatting with better structure
    // Check if the response already starts with "Answer:" or similar
    let cleanedResponse = response;

    // Remove any gibberish or corrupted text that might have slipped through
    cleanedResponse = cleanedResponse
      // Remove non-printable characters
      .replace(/[^\x20-\x7E\n\r\t\u00A0-\u00FF\u0100-\u017F\u0180-\u024F\u0250-\u02AF\u1E00-\u1EFF]+/g, ' ')
      // Replace characters repeated more than 5 times with just 3
      .replace(/(.)\1{5,}/g, '$1$1$1')
      // Keep only common punctuation and alphanumeric chars
      .replace(/[^\w\s.,;:!?'"()\[\]{}\-–—/\\&@#$%^*+=<>|~`]+/g, ' ')
      // Remove repeated "and" patterns (common in corrupted text)
      .replace(/(\s+and\s+(?:people|color|businesses|individuals|artisans|traders|various|all|diverse)(?:,|\s+and\s+)){3,}/gi, ' ')
      // Remove sections with excessive commas and "and" (typical pattern in corrupted text)
      .replace(/(?:,\s*and\s*){5,}[^.!?;:]{0,100}/g, '.')
      // Remove any remaining gibberish patterns (repeated words with commas)
      .replace(/(\w+(?:,\s*|\s+and\s+)){10,}/g, '... ')
      .trim();

    // Ensure the response starts with "Answer:" for proper display
    if (!cleanedResponse.startsWith('Answer:') && !cleanedResponse.startsWith('🧠')) {
      cleanedResponse = 'Answer: ' + cleanedResponse;
    }

    // Format the final response with research results header and sources
    const formattedResponse = `🧠 Research Results\n${cleanedResponse}\n\nSources:\n${searchResults.map(result => result.title).join('\n')}`;

    // Store successful responses in the RAG system for future learning
    // Only store responses that seem to be high quality (longer than 500 characters)
    if (cleanedResponse.length > 500 && !cleanedResponse.includes('Error') && !cleanedResponse.includes('could not')) {
      try {
        // Create a document with the successful response
        const successDoc = `DOCUMENT TYPE: successful_deep_research_response
TITLE: Deep Research Response Example
PURPOSE: Provide example of successful Deep Research response

QUERY: ${response.substring(0, 100)}...

RESPONSE:
${cleanedResponse}

This is an example of a successful Deep Research response that can be used as a reference for future responses.`;

        // Save to a temporary file
        const fs = require('fs');
        const os = require('os');
        const path = require('path');
        const tempFile = path.join(os.tmpdir(), 'successful_response.txt');
        fs.writeFileSync(tempFile, successDoc);

        // Add to the RAG system
        console.log('Storing successful response in RAG system for future learning');
        try {
          const addResult = await runPythonCommand(['add_document.py', tempFile, 'deep_research_example', 'Successful Deep Research Response']);
          console.log('Successful response stored in RAG system');
        } catch (pythonError) {
          console.error('Error running Python command:', pythonError);
          // Continue even if Python command fails
        }

        // Clean up
        fs.unlinkSync(tempFile);
      } catch (error) {
        console.error('Error storing successful response in RAG system:', error);
        // Continue even if storing fails
      }
    }

    return formattedResponse;
  } catch (error) {
    console.error('Error handling Deep Research response:', error);
    return `🧠 Research Results\n${response}\n\nSources:\n${searchResults.map(result => result.title).join('\n')}`;
  }
}

// Process a document for Deep Research
async function processDocumentForDeepResearch(data, documentSearchResult) {
  try {
    // Start a Python process to get the document content
    console.log(`Retrieving document content for ID: ${data.documentContext.documentId}`);
    mainWindow.webContents.send('log', 'Retrieving document content...');

    // Start a new Python process specifically for getting the document
    const { spawn } = require('child_process');
    const docProcess = spawn('python', ['main.py', '--get-document', data.documentContext.documentId]);

    let docOutput = '';
    let docError = '';

    // Collect output from the process
    docProcess.stdout.on('data', (data) => {
      docOutput += data.toString();
    });

    // Collect errors from the process
    docProcess.stderr.on('data', (data) => {
      docError += data.toString();
      console.error(`Document retrieval error: ${data}`);
    });

    // Wait for the process to complete
    const docResult = await new Promise((resolve, reject) => {
      docProcess.on('close', (code) => {
        if (code === 0) {
          resolve(docOutput);
        } else {
          reject(new Error(`Document retrieval process exited with code ${code}: ${docError}`));
        }
      });

      // Set a timeout to prevent hanging
      setTimeout(() => {
        docProcess.kill();
        reject(new Error('Document retrieval process timed out after 30 seconds'));
      }, 30000);
    });

    // Parse the document content
    let documentContent = '';
    if (docOutput.includes('document_content:')) {
      // Extract the document content
      const contentMatch = docOutput.match(/document_content:(.*)/s);
      if (contentMatch && contentMatch[1]) {
        documentContent = contentMatch[1].trim();
        console.log(`Successfully extracted document content (${documentContent.length} characters)`);
      }
    }

    if (!documentContent) {
      console.error('Failed to retrieve document content. Raw output:', docOutput);
      throw new Error('Could not retrieve document content. Check the console for details.');
    }

    // Create a content object for the document
    const extractedContent = {
      url: documentSearchResult.url,
      title: documentSearchResult.title,
      content: documentContent
    };

    // Analyze the document content with the LLM
    mainWindow.webContents.send('log', 'Analyzing document content with LLM...');

    // Prepare the prompt for the LLM
    const prompt = `I need you to analyze and summarize information from the following document to answer this question:

QUESTION: ${data.query}

I'll provide the content from the document "${data.documentContext.fileName}". Please analyze this information and provide:

1. A comprehensive answer to the question based ONLY on the information in this document
2. A detailed summary of the key points from the document
3. If the document doesn't contain sufficient information to fully answer the question, clearly state what information is missing

IMPORTANT INSTRUCTIONS:
- Base your analysis ONLY on the content provided in this document, not on your prior knowledge
- Do NOT make up information that isn't in the document
- If the document doesn't contain relevant information to answer the question, state this clearly
- Include ALL relevant information from the document
- Format your response with clear sections and headings
- Start your answer with "Answer:" and end with a "DOCUMENT SOURCE" section mentioning the document name

Here is the content from the document:

DOCUMENT: ${data.documentContext.fileName}

${documentContent}

Remember to focus ONLY on the information provided in this document to answer the question: "${data.query}"`;

    // Create a temporary file with the prompt
    const fs = require('fs');
    const os = require('os');
    const path = require('path');
    const tempDir = os.tmpdir();
    const tempFilePath = path.join(tempDir, 'document_analysis_prompt.txt');

    // Analyze the document
    const analysis = await analyzeWithLLM(prompt, tempFilePath, [extractedContent], data.query, data.model);

    // Process the analysis to check for insufficient information or tool suggestions
    const processedAnalysis = await handleDeepResearchResponse(analysis, [documentSearchResult]);

    // Send the processed analysis to the renderer
    mainWindow.webContents.send('deep-research-complete', {
      query: data.query,
      analysis: processedAnalysis,
      sources: [documentSearchResult]
    });

    // Also send a signal to re-enable the input field
    mainWindow.webContents.send('rag-query-complete');

    return { success: true };
  } catch (error) {
    console.error('Error processing document for Deep Research:', error.message);
    mainWindow.webContents.send('log', `Error processing document: ${error.message}`);

    // Send error to renderer
    mainWindow.webContents.send('deep-research-error', {
      error: error.message
    });

    // Also send a signal to re-enable the input field
    mainWindow.webContents.send('rag-query-complete');

    // Return an error object
    return {
      success: false,
      error: error.message
    };
  }
}

// Perform web search using Bing
async function performWebSearch(query, resultsCount = 3) {
  try {
    console.log(`Performing web search for: ${query} (${resultsCount} results)`);
    mainWindow.webContents.send('log', `Performing web search for: ${query} (requesting ${resultsCount} results)`);

    // Use the web-search tool to get real search results
    const { ipcMain } = require('electron');
    const https = require('https');

    // Use Bing search directly - request more results than needed to account for filtering
    // Request double the number to ensure we have enough after filtering
    const searchUrl = `https://www.bing.com/search?q=${encodeURIComponent(query)}&count=${resultsCount * 2}`;

    // Create an off-screen browser window to perform the search
    const { BrowserWindow } = require('electron');
    const searchWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      show: false, // Hide the window
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        images: false, // Disable images for faster loading
        javascript: true
      }
    });

    try {
      // Load the Bing search page
      await searchWindow.loadURL(searchUrl, {
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        timeout: 15000 // Increased timeout for better reliability
      });

      // Wait for the page to load completely - increased wait time
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Extract search results using page script
      const searchResults = await searchWindow.webContents.executeJavaScript(`
        (function() {
          try {
            // Find all search result elements - try multiple selectors for better reliability
            let resultElements = document.querySelectorAll('.b_algo');

            // If we didn't find enough results with .b_algo, try other selectors
            if (resultElements.length < ${resultsCount * 2}) {
              resultElements = [...resultElements, ...document.querySelectorAll('.b_ans')];
            }

            // If still not enough, try more generic selectors
            if (resultElements.length < ${resultsCount * 2}) {
              resultElements = [...resultElements, ...document.querySelectorAll('li.b_algo, li.b_ans, div.b_algo, div.b_ans')];
            }

            const results = [];
            const seenUrls = new Set(); // To avoid duplicate URLs

            // Extract data from each result
            for (let i = 0; i < resultElements.length && results.length < ${resultsCount * 2}; i++) {
              const element = resultElements[i];
              const titleElement = element.querySelector('h2 a') || element.querySelector('a');
              const snippetElement = element.querySelector('.b_caption p') || element.querySelector('p');

              if (titleElement && titleElement.href) {
                // Skip if we've already seen this URL
                if (seenUrls.has(titleElement.href)) continue;

                // Add to seen URLs
                seenUrls.add(titleElement.href);

                results.push({
                  title: titleElement.innerText || 'Bing Search Result',
                  url: titleElement.href,
                  snippet: snippetElement ? snippetElement.innerText : 'No description available'
                });
              }
            }

            return results;
          } catch (e) {
            // Return an empty array if there's an error
            console.error('Error extracting search results:', e);
            return [];
          }
        })();
      `);

      // Close the window to free resources
      searchWindow.close();

      // If we got results, return them
      if (searchResults && searchResults.length > 0) {
        // Limit to the requested number of results
        const limitedResults = searchResults.slice(0, resultsCount);
        console.log(`Found ${limitedResults.length} search results from Bing for query: ${query}`);
        mainWindow.webContents.send('log', `Found ${limitedResults.length} search results from Bing`);

        // Log each result for debugging
        limitedResults.forEach((result, index) => {
          console.log(`Result ${index + 1}: ${result.title} - ${result.url}`);
          mainWindow.webContents.send('log', `Result ${index + 1}: ${result.title}`);
        });

        return limitedResults;
      }

      // If no results, throw an error to trigger fallback
      throw new Error('No search results found on Bing');
    } catch (error) {
      // Close the window if it's still open
      if (searchWindow) {
        searchWindow.close();
      }

      // Re-throw the error to trigger fallback
      throw error;
    }
  } catch (error) {
    console.error('Error performing Bing search:', error.message);

    // Provide fallback results if the search fails
    console.log('Using fallback search results');

    // Generate some generic search results based on the query
    const fallbackResults = [];

    // Add some generic search results
    fallbackResults.push({
      title: 'Wikipedia - ' + query,
      url: `https://en.wikipedia.org/wiki/Special:Search?search=${encodeURIComponent(query)}`,
      snippet: `Information about ${query} from Wikipedia, the free encyclopedia.`
    });

    fallbackResults.push({
      title: 'Reddit - ' + query,
      url: `https://www.reddit.com/search/?q=${encodeURIComponent(query)}`,
      snippet: `Discussions about ${query} on Reddit.`
    });

    fallbackResults.push({
      title: 'YouTube - ' + query,
      url: `https://www.youtube.com/results?search_query=${encodeURIComponent(query)}`,
      snippet: `Videos about ${query} on YouTube.`
    });

    fallbackResults.push({
      title: 'GitHub - ' + query,
      url: `https://github.com/search?q=${encodeURIComponent(query)}`,
      snippet: `Code repositories related to ${query} on GitHub.`
    });

    fallbackResults.push({
      title: 'Medium - ' + query,
      url: `https://medium.com/search?q=${encodeURIComponent(query)}`,
      snippet: `Articles about ${query} on Medium.`
    });

    // Limit to the requested number of results
    const limitedResults = fallbackResults.slice(0, resultsCount);

    console.log(`Using ${limitedResults.length} fallback search results for query: ${query}`);

    return limitedResults;
  }
}

// Extract content from a URL using Electron's browser features with improved error handling
async function extractContentFromUrl(url) {
  try {
    console.log(`Extracting content from URL: ${url}`);
    mainWindow.webContents.send('log', `Extracting content from: ${url}`);

    // Create an off-screen browser window with a timeout
    const contentWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      show: false, // Hide the window
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        images: false, // Disable images for faster loading
        javascript: true,
        webSecurity: false // Allow cross-origin requests
      }
    });

    // Set a timeout to prevent hanging on problematic pages
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Timeout extracting content from ${url}`));
      }, 15000); // Increased to 15 second timeout for better reliability
    });

    try {
      // Race between the page load and the timeout
      await Promise.race([
        contentWindow.loadURL(url, {
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          timeout: 15000 // Increased timeout for better reliability
        }),
        timeoutPromise
      ]);

      // Wait longer for the page to load completely
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Extract content using page script with enhanced error handling for travel content
      const content = await contentWindow.webContents.executeJavaScript(`
        (function() {
          try {
            // Get the page title
            const title = document.title || '';

            // Function to clean text (remove excessive whitespace, etc.)
            function cleanContent(text) {
              if (!text) return '';

              // Remove extra whitespace and normalize line breaks
              return text
                .replace(/\\s+/g, ' ')
                .replace(/\\n{3,}/g, '\\n\\n')
                .replace(/cookie|cookies|privacy|terms|accept|subscribe|newsletter|sign up|email/gi, '')
                .replace(/advertisement|advert|ad|ads|sponsored/gi, '')
                .trim()
                .substring(0, 100000); // Increased limit for more comprehensive content
            }

            // Function to get the main content with improved reliability for travel content
            function getMainContent() {
              try {
                // Try to find the main content area with expanded selectors, including travel-specific ones
                const mainSelectors = [
                  // Travel-specific selectors
                  '.travel-content',
                  '.destination-content',
                  '.place-description',
                  '.attraction-details',
                  '.travel-guide',
                  '.destination-guide',
                  '.travel-info',
                  '.destination-info',
                  '.travel-article',
                  '.destination-article',
                  // Common article containers
                  'main',
                  'article',
                  '#content',
                  '.content',
                  '#main',
                  '.main',
                  '.post',
                  '.article',
                  '[role="main"]',
                  '.entry-content',
                  '.page-content',
                  '#article',
                  '.article-content',
                  '.post-content',
                  '.blog-post',
                  '.story',
                  '.text',
                  '.body',
                  '.container',
                  '#primary',
                  '.primary',
                  '#page-content',
                  '.page',
                  '#wrapper',
                  '.wrapper'
                ];

                // Try each selector and collect all potential content
                let allContent = [];

                // First pass: try to find the best main content container
                for (const selector of mainSelectors) {
                  const elements = document.querySelectorAll(selector);
                  for (const element of elements) {
                    if (element && element.innerText && element.innerText.length > 200) {
                      allContent.push({
                        text: element.innerText,
                        length: element.innerText.length,
                        element: element
                      });
                    }
                  }
                }

                // Sort by content length (descending)
                allContent.sort((a, b) => b.length - a.length);

                // If we found good content, return the largest
                if (allContent.length > 0) {
                  return allContent[0].text;
                }

                // Look specifically for travel-related content
                const travelKeywords = ['travel', 'destination', 'visit', 'attraction', 'tour', 'place', 'thailand', 'beach', 'island', 'city'];
                const allElements = document.querySelectorAll('div, section, article');

                for (const element of allElements) {
                  const text = element.innerText || '';
                  if (text.length > 500) {
                    // Check if this element contains travel keywords
                    const lowerText = text.toLowerCase();
                    const keywordMatches = travelKeywords.filter(keyword => lowerText.includes(keyword)).length;

                    if (keywordMatches >= 2) {
                      return text;
                    }
                  }
                }

                // If no main content area found with selectors, try to find the largest text block
                const paragraphs = Array.from(document.querySelectorAll('p'));
                if (paragraphs.length > 0) {
                  // Sort paragraphs by text length (descending)
                  paragraphs.sort((a, b) => b.innerText.length - a.innerText.length);

                  // Get the parent of the largest paragraph
                  const largestParagraph = paragraphs[0];
                  const parent = largestParagraph.parentElement;

                  if (parent && parent.innerText.length > 200) {
                    return parent.innerText;
                  }

                  // If parent doesn't have enough content, combine the top paragraphs
                  let combinedText = '';
                  const topParagraphs = paragraphs.slice(0, 20); // Take top 20 paragraphs
                  topParagraphs.forEach(p => {
                    if (p.innerText && p.innerText.trim().length > 20) {
                      combinedText += p.innerText + '\\n\\n';
                    }
                  });

                  if (combinedText.length > 200) {
                    return combinedText;
                  }
                }

                // Try to get headings and their following content
                const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
                if (headings.length > 0) {
                  let structuredContent = '';
                  headings.forEach(heading => {
                    if (heading.innerText && heading.innerText.trim().length > 0) {
                      structuredContent += heading.innerText + ':\\n';

                      // Get the next siblings until we hit another heading
                      let nextElement = heading.nextElementSibling;
                      while (nextElement && !nextElement.tagName.match(/^H[1-6]$/i)) {
                        if (nextElement.innerText && nextElement.innerText.trim().length > 0) {
                          structuredContent += nextElement.innerText + '\\n\\n';
                        }
                        nextElement = nextElement.nextElementSibling;
                      }
                    }
                  });

                  if (structuredContent.length > 200) {
                    return structuredContent;
                  }
                }

                // If still no good content, use the body text
                return document.body.innerText;
              } catch (e) {
                // Fallback to body text if any error occurs
                console.error('Error in getMainContent:', e);
                return document.body.innerText || '';
              }
            }

            // Get the main content
            const mainContent = getMainContent();

            // Get meta description as fallback
            const metaDescription = document.querySelector('meta[name="description"]')?.content || '';

            // Get any structured data that might be available (especially for travel content)
            let structuredData = '';
            try {
              const jsonLdScripts = document.querySelectorAll('script[type="application/ld+json"]');
              if (jsonLdScripts.length > 0) {
                for (const script of jsonLdScripts) {
                  try {
                    const data = JSON.parse(script.textContent);
                    if (data) {
                      // Extract relevant information from structured data
                      if (data.description) structuredData += '\\n' + data.description;
                      if (data.about) structuredData += '\\n' + data.about;
                      if (data.name) structuredData += '\\n' + data.name;

                      // Look for travel-specific information
                      if (data.touristType) structuredData += '\\n' + data.touristType;
                      if (data.touristDestination) structuredData += '\\n' + data.touristDestination;
                      if (data.amenityFeature) structuredData += '\\n' + data.amenityFeature;

                      // Look for nested information
                      if (data.mainEntity && data.mainEntity.description) {
                        structuredData += '\\n' + data.mainEntity.description;
                      }
                    }
                  } catch (e) {
                    // Ignore JSON parse errors
                  }
                }
              }
            } catch (e) {
              // Ignore structured data errors
            }

            // Extract any list items that might contain travel destinations
            let listItems = '';
            try {
              const lists = document.querySelectorAll('ul, ol');
              for (const list of lists) {
                const items = list.querySelectorAll('li');
                if (items.length > 3 && items.length < 30) {  // Reasonable list size for destinations
                  const listText = Array.from(items).map(item => item.innerText.trim()).join('\\n- ');
                  if (listText.length > 100) {
                    listItems += '\\n- ' + listText;
                  }
                }
              }
            } catch (e) {
              // Ignore list extraction errors
            }

            // Combine all content sources
            const combinedContent = [
              mainContent,
              structuredData,
              listItems,
              metaDescription
            ].filter(Boolean).join('\\n\\n');

            return {
              title: title,
              content: cleanContent(combinedContent) || 'No content extracted',
              url: window.location.href
            };
          } catch (e) {
            // Return minimal information if script fails
            return {
              title: document.title || '',
              content: 'Error extracting content: ' + e.message,
              url: window.location.href
            };
          }
        })();
      `);

      console.log(`Successfully extracted content from ${url}, title: ${content.title}`);

      // Ensure we have some content
      if (!content.content || content.content.length < 100) {
        console.warn(`Warning: Extracted very little content from ${url}`);
      }

      // Further clean the content on the Node.js side with enhanced cleaning
      let cleanedContent = content.content;
      if (cleanedContent) {
        // First pass: basic cleaning
        cleanedContent = cleanedContent
          .replace(/\n{3,}/g, '\n\n') // Replace multiple newlines with double newlines
          .replace(/\s{2,}/g, ' ') // Replace multiple spaces with a single space
          .replace(/cookie|cookies|privacy|terms|accept|subscribe|newsletter|sign up|email/gi, '') // Remove cookie/privacy notices
          .replace(/advertisement|advert|ad|ads|sponsored/gi, '') // Remove ad notices
          // Remove common web page noise
          .replace(/share|facebook|twitter|instagram|pinterest|linkedin|youtube|subscribe|newsletter|sign up|login|register|create account/gi, '')
          // Remove navigation elements
          .replace(/menu|navigation|nav|header|footer|sidebar|copyright|all rights reserved/gi, '')
          // Remove common UI elements
          .replace(/click|tap|swipe|scroll|button|icon|dropdown|popup|modal|dialog|alert|notification/gi, '')
          .trim();

        // Second pass: remove any remaining gibberish or corrupted text
        cleanedContent = cleanedContent
          .replace(/[^\x20-\x7E\n\r\t\u00A0-\u00FF\u0100-\u017F\u0180-\u024F\u0250-\u02AF\u1E00-\u1EFF]+/g, ' ')
          .replace(/(.)\1{5,}/g, '$1$1$1') // Replace characters repeated more than 5 times with just 3
          .replace(/[^\w\s.,;:!?'"()\[\]{}\-–—/\\&@#$%^*+=<>|~`]+/g, ' ') // Keep only common punctuation and alphanumeric chars
          .trim();

        // Third pass: check for gibberish content by analyzing character distribution
        const letterCount = (cleanedContent.match(/[a-zA-Z]/g) || []).length;
        const totalLength = cleanedContent.length;
        const letterRatio = totalLength > 0 ? letterCount / totalLength : 0;

        // If the content has very few letters relative to its length, it's likely gibberish
        if (totalLength > 100 && letterRatio < 0.4) {
          console.warn(`Content from ${url} appears to be gibberish (letter ratio: ${letterRatio.toFixed(2)})`);
          cleanedContent = `The content from this page appears to be unreadable or corrupted. Please check another source for information about this topic.`;
        }

        // Check for repetitive patterns that indicate scraping issues
        const repetitivePatterns = [
          /(.{20,})\1{3,}/g,  // Same exact string repeated 3+ times
          /(cookie|privacy|terms|accept).{0,20}(cookie|privacy|terms|accept).{0,20}(cookie|privacy|terms|accept)/gi, // Cookie notices
          /(error|forbidden|unauthorized|access denied|not found|404|403|500)/gi // Error messages
        ];

        for (const pattern of repetitivePatterns) {
          if (pattern.test(cleanedContent)) {
            console.warn(`Content from ${url} contains repetitive patterns or error messages`);
            cleanedContent = `The content from this page appears to contain repetitive elements or error messages. Please check another source for information about this topic.`;
            break;
          }
        }
      }

      return {
        url: content.url || url,
        title: content.title || url.split('/').pop() || 'Untitled Page',
        content: cleanedContent || `No content could be extracted from ${url}`
      };
    } catch (error) {
      console.warn(`Warning: Could not extract content from ${url}: ${error.message}`);

      // Return a placeholder instead of throwing an error
      return {
        url: url,
        title: url.split('/').pop() || 'Untitled Page',
        content: `Could not access this page: ${error.message}. This URL may require authentication or have access restrictions.`
      };
    } finally {
      // Close the window to free resources
      contentWindow.close();
    }
  } catch (error) {
    console.error(`Error in content extraction process for ${url}:`, error.message);

    // Return a placeholder instead of throwing an error
    return {
      url: url,
      title: url.split('/').pop() || 'Untitled Page',
      content: `Error during content extraction: ${error.message}`
    };
  }
}

// Analyze extracted content with LLM - this function is now just a placeholder
// as the actual analysis is done directly in the deep-research handler
async function analyzeContentWithLLM(query, extractedContents, modelName) {
  // This function is kept for backward compatibility but is no longer used
  console.log(`analyzeContentWithLLM is now a placeholder - analysis is done directly in deep-research handler`);
  return 'Analysis in progress... The results will appear shortly.';
}

// Save research data to the database
async function saveResearchToDatabase(query, extractedContents, searchResults) {
  try {
    console.log(`Saving research data to database for query: ${query}`);

    // Prepare the data for saving
    const timestamp = new Date().toISOString();
    const researchData = {
      query: query,
      timestamp: timestamp,
      sources: searchResults.map(result => result.url),
      content: extractedContents.map(item => ({
        url: item.url,
        title: item.title,
        content: item.content
      }))
    };

    // Send the command to save the research data
    await sendToPython(`save_research_data:${JSON.stringify(researchData)}`);

    console.log('Research data saved to database');
    return true;
  } catch (error) {
    console.error('Error saving research data to database:', error.message);
    throw new Error(`Failed to save research data: ${error.message}`);
  }
}

// Initialize the database by starting Python in a special mode
function initializeDatabase() {
  console.log('Initializing database...');

  // Start Python process with a special flag to just initialize the database
  const initProcess = spawn('python', ['main.py', '--init-db-only']);

  // Set up basic handlers
  initProcess.stdout.on('data', (data) => {
    const output = data.toString().trim();
    console.log(`Database init stdout: ${output}`);

    // Forward to the UI if window is ready
    if (mainWindow) {
      mainWindow.webContents.send('python-output', output);

      // Check for database initialization
      if (output.includes('Database') &&
          (output.includes('initialized') || output.includes('connected') || output.includes('created'))) {
        mainWindow.webContents.send('database-status', output);
      }
    }
  });

  initProcess.stderr.on('data', (data) => {
    const error = data.toString().trim();
    console.error(`Database init stderr: ${error}`);

    // Forward to the UI if window is ready
    if (mainWindow) {
      mainWindow.webContents.send('python-error', error);
    }
  });

  initProcess.on('close', (code) => {
    console.log(`Database initialization process exited with code ${code}`);

    // If there was an error, notify the UI
    if (code !== 0 && mainWindow) {
      mainWindow.webContents.send('log', `Database initialization failed with code ${code}`);
    } else {
      // Check if we have existing data in the database
      checkExistingData();
    }
  });
}

// Check if we have existing data in the database
function checkExistingData() {
  console.log('Checking for existing data...');

  // Start Python process to check for existing data
  const checkProcess = spawn('python', ['main.py', '--check-data']);

  checkProcess.stdout.on('data', (data) => {
    const output = data.toString().trim();
    console.log(`Data check stdout: ${output}`);

    // Forward to the UI if window is ready
    if (mainWindow) {
      // Check for data status
      if (output.includes('existing_data:')) {
        const hasData = output.includes('existing_data:true');
        mainWindow.webContents.send('existing-data', hasData);

        if (hasData) {
          mainWindow.webContents.send('log', 'Found existing data in the database. You can analyze it without crawling again.');
        }
      }
    }
  });

  checkProcess.stderr.on('data', (data) => {
    const error = data.toString().trim();
    console.error(`Data check stderr: ${error}`);
  });

  checkProcess.on('close', (code) => {
    console.log(`Data check process exited with code ${code}`);
  });
}

// Pinecone operations
ipcMain.handle('configure-pinecone', async (event, data) => {
  try {
    console.log('Configuring Pinecone...');

    // Update user settings
    userSettings.pineconeApiKey = data.apiKey;
    userSettings.pineconeEnvironment = data.environment;
    userSettings.pineconeIndexName = data.indexName;
    userSettings.usePinecone = data.usePinecone;

    // Save settings
    saveSettings();

    // Start Python process if not running
    if (!pythonProcess) {
      console.log('Starting Python process for Pinecone configuration...');
      mainWindow.webContents.send('log', 'Starting Python process for Pinecone configuration...');

      // Start Python process with existing data
      pythonProcess = spawn('python', ['main.py', '--analyze-existing']);

      // Set up data handling
      setupPythonProcessHandlers(pythonProcess);

      // Notify the renderer that the Python process has started
      mainWindow.webContents.send('python-start');

      // Wait for the process to initialize
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Set up a flag to track if we've sent the RAG choice
      let ragChoiceSent = false;

      // Set up a one-time listener for the analysis choice prompt
      const choiceHandler = (data) => {
        const output = data.toString().trim();

        // Check if this is the analysis choice prompt and we haven't sent the choice yet
        if (!ragChoiceSent &&
            (output.includes('Choose analysis method:') ||
             (output.includes('1. RAG-based Q&A with OpenRouter') && output.includes('2. RAG-based Q&A without LLM')))) {

          console.log('Detected analysis choice prompt for Pinecone configuration, selecting option 2');

          // Set the flag to prevent duplicate sends
          ragChoiceSent = true;

          // Small delay before responding
          setTimeout(() => {
            try {
              // Select RAG-only option (2) - faster and doesn't require API key
              pythonProcess.stdin.write('2\n');
              console.log('Automatically selected RAG-only (option 2)');

              // Notify the UI
              mainWindow.webContents.send('log', 'Automatically selected RAG-based Q&A for Pinecone configuration');

              // Remove the listener after handling
              pythonProcess.stdout.removeListener('data', choiceHandler);
            } catch (err) {
              console.error('Error sending analysis choice:', err);
            }
          }, 500);
        }
      };

      // Add the listener
      pythonProcess.stdout.on('data', choiceHandler);

      // Wait for the RAG system to initialize
      await new Promise(resolve => setTimeout(resolve, 3000));
    }

    // Send the configuration to Python
    const configCommand = `pinecone_config:${JSON.stringify({
      api_key: data.apiKey,
      environment: data.environment,
      index_name: data.indexName,
      use_pinecone: data.usePinecone
    })}`;

    await sendToPython(configCommand);

    // Return success
    return { success: true };
  } catch (error) {
    console.error('Error configuring Pinecone:', error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('test-pinecone-connection', async (event) => {
  try {
    console.log('Testing Pinecone connection...');

    if (!pythonProcess) {
      // Start Python process if not running
      console.log('Starting Python process for Pinecone test...');
      mainWindow.webContents.send('log', 'Starting Python process for Pinecone test...');

      // Start Python process with existing data
      pythonProcess = spawn('python', ['main.py', '--analyze-existing']);

      // Set up data handling
      setupPythonProcessHandlers(pythonProcess);

      // Notify the renderer that the Python process has started
      mainWindow.webContents.send('python-start');

      // Wait for the process to initialize
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Set up a flag to track if we've sent the RAG choice
      let ragChoiceSent = false;

      // Set up a one-time listener for the analysis choice prompt
      const choiceHandler = (data) => {
        const output = data.toString().trim();

        // Check if this is the analysis choice prompt and we haven't sent the choice yet
        if (!ragChoiceSent &&
            (output.includes('Choose analysis method:') ||
             (output.includes('1. RAG-based Q&A with OpenRouter') && output.includes('2. RAG-based Q&A without LLM')))) {

          console.log('Detected analysis choice prompt for Pinecone test, selecting option 2');

          // Set the flag to prevent duplicate sends
          ragChoiceSent = true;

          // Small delay before responding
          setTimeout(() => {
            try {
              // Select RAG-only option (2) - faster and doesn't require API key
              pythonProcess.stdin.write('2\n');
              console.log('Automatically selected RAG-only (option 2)');

              // Notify the UI
              mainWindow.webContents.send('log', 'Automatically selected RAG-based Q&A for Pinecone test');

              // Remove the listener after handling
              pythonProcess.stdout.removeListener('data', choiceHandler);
            } catch (err) {
              console.error('Error sending analysis choice:', err);
            }
          }, 500);
        }
      };

      // Add the listener
      pythonProcess.stdout.on('data', choiceHandler);

      // Wait for the RAG system to initialize
      await new Promise(resolve => setTimeout(resolve, 3000));
    }

    // Send test command to Python
    await sendToPython('pinecone_test_connection');

    // We'll get the result through the normal output channel
    // The Python process will send a message that we'll capture in the output handler

    return { success: true, message: 'Testing connection...' };
  } catch (error) {
    console.error('Error testing Pinecone connection:', error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('upload-to-pinecone', async (event) => {
  try {
    console.log('Uploading vectors to Pinecone...');

    if (!pythonProcess) {
      // Start Python process if not running
      console.log('Starting Python process for Pinecone upload...');
      mainWindow.webContents.send('log', 'Starting Python process for Pinecone upload...');

      // Start Python process with existing data
      pythonProcess = spawn('python', ['main.py', '--analyze-existing']);

      // Set up data handling
      setupPythonProcessHandlers(pythonProcess);

      // Notify the renderer that the Python process has started
      mainWindow.webContents.send('python-start');

      // Wait for the process to initialize
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Set up a flag to track if we've sent the RAG choice
      let ragChoiceSent = false;

      // Set up a one-time listener for the analysis choice prompt
      const choiceHandler = (data) => {
        const output = data.toString().trim();

        // Check if this is the analysis choice prompt and we haven't sent the choice yet
        if (!ragChoiceSent &&
            (output.includes('Choose analysis method:') ||
             (output.includes('1. RAG-based Q&A with OpenRouter') && output.includes('2. RAG-based Q&A without LLM')))) {

          console.log('Detected analysis choice prompt for Pinecone upload, selecting option 2');

          // Set the flag to prevent duplicate sends
          ragChoiceSent = true;

          // Small delay before responding
          setTimeout(() => {
            try {
              // Select RAG-only option (2) - faster and doesn't require API key
              pythonProcess.stdin.write('2\n');
              console.log('Automatically selected RAG-only (option 2)');

              // Notify the UI
              mainWindow.webContents.send('log', 'Automatically selected RAG-based Q&A for Pinecone upload');

              // Remove the listener after handling
              pythonProcess.stdout.removeListener('data', choiceHandler);
            } catch (err) {
              console.error('Error sending analysis choice:', err);
            }
          }, 500);
        }
      };

      // Add the listener
      pythonProcess.stdout.on('data', choiceHandler);

      // Wait for the RAG system to initialize
      await new Promise(resolve => setTimeout(resolve, 3000));
    }

    // Send upload command to Python
    await sendToPython('pinecone_upload_vectors');

    // We'll get progress updates through the normal output channel

    return { success: true, message: 'Starting upload...' };
  } catch (error) {
    console.error('Error uploading to Pinecone:', error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('switch-vector-store', async (event, usePinecone) => {
  try {
    console.log(`Switching vector store to ${usePinecone ? 'Pinecone' : 'local FAISS'}...`);

    // Update user settings
    userSettings.usePinecone = usePinecone;

    // Save settings
    saveSettings();

    if (!pythonProcess) {
      // Start Python process if not running
      console.log('Starting Python process for vector store switch...');
      mainWindow.webContents.send('log', 'Starting Python process for vector store switch...');

      // Start Python process with existing data
      pythonProcess = spawn('python', ['main.py', '--analyze-existing']);

      // Set up data handling
      setupPythonProcessHandlers(pythonProcess);

      // Notify the renderer that the Python process has started
      mainWindow.webContents.send('python-start');

      // Wait for the process to initialize
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Set up a flag to track if we've sent the RAG choice
      let ragChoiceSent = false;

      // Set up a one-time listener for the analysis choice prompt
      const choiceHandler = (data) => {
        const output = data.toString().trim();

        // Check if this is the analysis choice prompt and we haven't sent the choice yet
        if (!ragChoiceSent &&
            (output.includes('Choose analysis method:') ||
             (output.includes('1. RAG-based Q&A with OpenRouter') && output.includes('2. RAG-based Q&A without LLM')))) {

          console.log('Detected analysis choice prompt for vector store switch, selecting option 2');

          // Set the flag to prevent duplicate sends
          ragChoiceSent = true;

          // Small delay before responding
          setTimeout(() => {
            try {
              // Select RAG-only option (2) - faster and doesn't require API key
              pythonProcess.stdin.write('2\n');
              console.log('Automatically selected RAG-only (option 2)');

              // Notify the UI
              mainWindow.webContents.send('log', 'Automatically selected RAG-based Q&A for vector store switch');

              // Remove the listener after handling
              pythonProcess.stdout.removeListener('data', choiceHandler);
            } catch (err) {
              console.error('Error sending analysis choice:', err);
            }
          }, 500);
        }
      };

      // Add the listener
      pythonProcess.stdout.on('data', choiceHandler);

      // Wait for the RAG system to initialize
      await new Promise(resolve => setTimeout(resolve, 3000));
    }

    // Send switch command to Python
    await sendToPython(`pinecone_switch:${usePinecone ? 'true' : 'false'}`);

    return { success: true };
  } catch (error) {
    console.error('Error switching vector store:', error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-pinecone-stats', async (event) => {
  try {
    console.log('Getting Pinecone stats...');

    if (!pythonProcess) {
      // Start Python process if not running
      console.log('Starting Python process for Pinecone stats...');
      mainWindow.webContents.send('log', 'Starting Python process for Pinecone stats...');

      // Start Python process with existing data
      pythonProcess = spawn('python', ['main.py', '--analyze-existing']);

      // Set up data handling
      setupPythonProcessHandlers(pythonProcess);

      // Notify the renderer that the Python process has started
      mainWindow.webContents.send('python-start');

      // Wait for the process to initialize
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Set up a flag to track if we've sent the RAG choice
      let ragChoiceSent = false;

      // Set up a one-time listener for the analysis choice prompt
      const choiceHandler = (data) => {
        const output = data.toString().trim();

        // Check if this is the analysis choice prompt and we haven't sent the choice yet
        if (!ragChoiceSent &&
            (output.includes('Choose analysis method:') ||
             (output.includes('1. RAG-based Q&A with OpenRouter') && output.includes('2. RAG-based Q&A without LLM')))) {

          console.log('Detected analysis choice prompt for Pinecone stats, selecting option 2');

          // Set the flag to prevent duplicate sends
          ragChoiceSent = true;

          // Small delay before responding
          setTimeout(() => {
            try {
              // Select RAG-only option (2) - faster and doesn't require API key
              pythonProcess.stdin.write('2\n');
              console.log('Automatically selected RAG-only (option 2)');

              // Notify the UI
              mainWindow.webContents.send('log', 'Automatically selected RAG-based Q&A for Pinecone stats');

              // Remove the listener after handling
              pythonProcess.stdout.removeListener('data', choiceHandler);
            } catch (err) {
              console.error('Error sending analysis choice:', err);
            }
          }, 500);
        }
      };

      // Add the listener
      pythonProcess.stdout.on('data', choiceHandler);

      // Wait for the RAG system to initialize
      await new Promise(resolve => setTimeout(resolve, 3000));
    }

    // Send stats command to Python
    await sendToPython('pinecone_get_stats');

    // We'll get the result through the normal output channel

    return { success: true, message: 'Retrieving stats...' };
  } catch (error) {
    console.error('Error getting Pinecone stats:', error.message);
    return { success: false, error: error.message };
  }
});

// Initialize the conversation manager
async function initializeConversationManager() {
  try {
    console.log('Initializing conversation manager...');
    conversationManager = new ConversationManager();
    await conversationManager.initDatabase();
    console.log('Conversation manager initialized successfully');

    // Try to get the active session
    const activeSession = await conversationManager.getActiveSession();
    if (activeSession) {
      console.log(`Active session found: ${activeSession.id} (${activeSession.name})`);
      activeSessionId = activeSession.id;
    } else {
      console.log('No active session found, checking if any sessions exist...');

      // Check if there are any sessions
      const sessions = await conversationManager.getSessions();
      if (sessions.length === 0) {
        // No sessions exist, create a default session
        console.log('No sessions found, creating default session...');
        const defaultSession = await conversationManager.createSession('Default Session');
        console.log(`Created default session: ${defaultSession.id} (${defaultSession.name})`);
        activeSessionId = defaultSession.id;
      } else {
        console.log(`Found ${sessions.length} sessions, but none active. Will set first one as active.`);
        // Set the first session as active
        await conversationManager.setActiveSession(sessions[0].id);
        activeSessionId = sessions[0].id;
        console.log(`Set session ${sessions[0].id} (${sessions[0].name}) as active`);
      }
    }
  } catch (error) {
    console.error('Error initializing conversation manager:', error);
  }
}

// App lifecycle events
app.on('ready', () => {
  createWindow();
  initializeDatabase();
  initializeConversationManager();

  // Load settings after a short delay to ensure the window is ready
  setTimeout(() => {
    loadSettings();
  }, 2000);
});

app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') {
    if (pythonProcess) {
      pythonProcess.kill();
    }

    // Close the conversation manager
    if (conversationManager) {
      console.log('Closing conversation manager...');
      conversationManager.close();
    }

    app.quit();
  }
});

app.on('activate', function () {
  if (mainWindow === null) {
    createWindow();
    initializeDatabase();
    initializeConversationManager();
  }
});

// Conversation management IPC handlers
ipcMain.handle('get-sessions', async () => {
  try {
    if (!conversationManager) {
      await initializeConversationManager();
    }
    return await conversationManager.getSessions();
  } catch (error) {
    console.error('Error getting sessions:', error);
    return { error: error.message };
  }
});

ipcMain.handle('create-session', async (event, name) => {
  try {
    if (!conversationManager) {
      await initializeConversationManager();
    }
    return await conversationManager.createSession(name);
  } catch (error) {
    console.error('Error creating session:', error);
    return { error: error.message };
  }
});

ipcMain.handle('set-active-session', async (event, sessionId) => {
  try {
    if (!conversationManager) {
      await initializeConversationManager();
    }
    return await conversationManager.setActiveSession(sessionId);
  } catch (error) {
    console.error('Error setting active session:', error);
    return { error: error.message };
  }
});

ipcMain.handle('delete-session', async (event, sessionId) => {
  try {
    if (!conversationManager) {
      await initializeConversationManager();
    }
    return await conversationManager.deleteSession(sessionId);
  } catch (error) {
    console.error('Error deleting session:', error);
    return { error: error.message };
  }
});

ipcMain.handle('get-session-messages', async (event, sessionId) => {
  try {
    if (!conversationManager) {
      await initializeConversationManager();
    }
    return await conversationManager.getSessionMessages(sessionId);
  } catch (error) {
    console.error('Error getting session messages:', error);
    return { error: error.message };
  }
});

ipcMain.handle('add-message', async (event, data) => {
  try {
    if (!conversationManager) {
      console.log('Initializing conversation manager before adding message');
      await initializeConversationManager();
    }

    console.log(`Adding message from ${data.sender} to conversation`);
    const result = await conversationManager.addMessage(data.sender, data.content);
    console.log(`Message added successfully, ID: ${result.id}`);
    return result;
  } catch (error) {
    console.error('Error adding message:', error);
    return { error: error.message };
  }
});

ipcMain.handle('get-relevant-conversation-history', async (event, query) => {
  try {
    if (!conversationManager) {
      await initializeConversationManager();
    }

    // Get the active session
    const activeSession = await conversationManager.getActiveSession();
    if (!activeSession) {
      return { error: 'No active session found' };
    }

    // Get relevant messages for the query
    const relevantMessages = await conversationManager.getRelevantMessages(query, activeSession.id);

    // Format the messages for the LLM context
    const formattedHistory = conversationManager.formatMessagesForContext(relevantMessages);

    return {
      sessionId: activeSession.id,
      messages: relevantMessages,
      formattedHistory
    };
  } catch (error) {
    console.error('Error getting relevant conversation history:', error);
    return { error: error.message };
  }
});

// ===== MULTI-QUERY DEEP RESEARCH HELPER FUNCTIONS =====

// Generate sub-queries for multi-query research
async function generateSubQueries(originalQuery, maxQueries, model) {
  try {
    console.log(`Generating up to ${maxQueries} sub-queries for: "${originalQuery}"`);

    const prompt = `You are a research assistant helping to break down a complex query into focused search queries.

Original query: "${originalQuery}"

Generate ${Math.min(maxQueries, 5)} specific, focused search queries that will help gather comprehensive information about this topic. Each query should target a different aspect or angle of the original question.

Guidelines:
- Make each query specific and searchable
- Avoid overlapping queries
- Focus on different aspects, perspectives, or components
- Use keywords that would work well in web searches
- Keep queries concise but descriptive

Respond with EXACTLY this format:
SUB_QUERIES:
1. [First focused query]
2. [Second focused query]
3. [Third focused query]
[etc.]

Example for "Impact of AI on healthcare":
SUB_QUERIES:
1. AI medical diagnosis accuracy studies 2024
2. AI healthcare cost reduction benefits
3. AI medical imaging radiology applications
4. AI drug discovery pharmaceutical research
5. AI healthcare privacy security concerns`;

    const apiKey = process.env.OPENROUTER_API_KEY || userSettings.openrouterApiKey;
    if (!apiKey) {
      throw new Error('API key required for sub-query generation');
    }

    const { default: fetch } = await import('node-fetch');
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'HTTP-Referer': 'http://localhost:3000',
        'X-Title': 'Fungi Multi-Query Generation'
      },
      body: JSON.stringify({
        model: model || userSettings.defaultModel,
        messages: [
          { role: 'user', content: prompt }
        ]
      })
    });

    if (!response.ok) {
      throw new Error(`LLM API error: ${response.status}`);
    }

    const result = await response.json();
    const content = result.choices[0].message.content;

    // Parse the sub-queries
    const subQueries = [];
    const lines = content.split('\n');
    let inSubQueriesSection = false;

    for (const line of lines) {
      if (line.includes('SUB_QUERIES:')) {
        inSubQueriesSection = true;
        continue;
      }

      if (inSubQueriesSection && line.trim()) {
        // Extract query from numbered list format
        const match = line.match(/^\d+\.\s*(.+)$/);
        if (match && match[1]) {
          subQueries.push(match[1].trim());
        }
      }
    }

    console.log(`Generated ${subQueries.length} sub-queries:`, subQueries);
    return subQueries.slice(0, maxQueries); // Ensure we don't exceed the limit

  } catch (error) {
    console.error('Error generating sub-queries:', error);
    return []; // Return empty array to trigger fallback
  }
}

// Extract content with timeout handling
async function extractContentWithTimeout(searchResults, queryContext, queryIndex, totalQueries, customTimeout = null) {
  const extractedContents = [];
  const timeout = customTimeout || userSettings.deepResearchUrlTimeout || 10000; // Use custom timeout or default

  for (let i = 0; i < searchResults.length; i++) {
    const result = searchResults[i];
    mainWindow.webContents.send('log', `Query ${queryIndex}/${totalQueries}: Extracting ${i+1}/${searchResults.length} - ${result.title}`);

    try {
      // Extract content with timeout and retry logic
      const content = await extractContentWithRetry(result.url, timeout);
      extractedContents.push(content);

      // Short delay between extractions
      if (i < searchResults.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    } catch (error) {
      console.warn(`Failed to extract content from ${result.url}:`, error.message);
      mainWindow.webContents.send('log', `Warning: Could not extract from ${result.url}`);

      // Add placeholder for failed extractions
      extractedContents.push({
        url: result.url,
        title: result.title || 'Unknown Page',
        content: `Could not extract content: ${error.message}`
      });
    }
  }

  return extractedContents;
}

// Extract content with retry logic and timeout
async function extractContentWithRetry(url, timeout) {
  const maxRetries = 2;
  let lastError;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`Extracting content from ${url} (attempt ${attempt}/${maxRetries})`);

      // Create a timeout promise
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Timeout')), timeout);
      });

      // Race between content extraction and timeout
      const content = await Promise.race([
        extractContentFromUrl(url),
        timeoutPromise
      ]);

      // Check if content is meaningful
      if (content.content && content.content.length > 200) {
        console.log(`Successfully extracted ${content.content.length} characters from ${url}`);
        return content;
      } else {
        throw new Error('Insufficient content extracted');
      }

    } catch (error) {
      lastError = error;
      console.warn(`Attempt ${attempt} failed for ${url}: ${error.message}`);

      if (attempt < maxRetries) {
        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
      }
    }
  }

  // If all attempts failed, throw the last error
  throw lastError;
}

// Remove duplicate results based on URL
function removeDuplicateResults(searchResults, extractedContents) {
  const seenUrls = new Set();
  const uniqueSearchResults = [];
  const uniqueExtractedContents = [];

  for (let i = 0; i < searchResults.length; i++) {
    const url = searchResults[i].url;
    if (!seenUrls.has(url)) {
      seenUrls.add(url);
      uniqueSearchResults.push(searchResults[i]);
      if (i < extractedContents.length) {
        uniqueExtractedContents.push(extractedContents[i]);
      }
    }
  }

  return {
    searchResults: uniqueSearchResults,
    extractedContents: uniqueExtractedContents
  };
}

// Analyze content gaps and suggest additional queries
async function analyzeContentGaps(originalQuery, extractedContents, model) {
  try {
    console.log('Analyzing content gaps for potential follow-up queries');

    // Create a summary of what we have
    const contentSummary = extractedContents
      .filter(content => content.content && content.content.length > 200)
      .map(content => `- ${content.title}: ${content.content.substring(0, 200)}...`)
      .join('\n');

    if (!contentSummary) {
      return { needsMoreQueries: false, additionalQueries: [] };
    }

    const prompt = `You are analyzing search results to identify information gaps.

Original query: "${originalQuery}"

Current search results summary:
${contentSummary}

Analyze the above content and determine:
1. Are there significant gaps in information that would help answer the original query?
2. What specific additional search queries would fill these gaps?

Respond with EXACTLY this format:

NEEDS_MORE_QUERIES: [YES/NO]
REASONING: [Brief explanation of gaps found or why no more queries needed]
ADDITIONAL_QUERIES:
1. [First additional query if needed]
2. [Second additional query if needed]
[etc.]

Only suggest additional queries if there are clear, important gaps in the information.`;

    const apiKey = process.env.OPENROUTER_API_KEY || userSettings.openrouterApiKey;
    if (!apiKey) {
      return { needsMoreQueries: false, additionalQueries: [] };
    }

    const { default: fetch } = await import('node-fetch');
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'HTTP-Referer': 'http://localhost:3000',
        'X-Title': 'Fungi Gap Analysis'
      },
      body: JSON.stringify({
        model: model || userSettings.defaultModel,
        messages: [
          { role: 'user', content: prompt }
        ]
      })
    });

    if (!response.ok) {
      return { needsMoreQueries: false, additionalQueries: [] };
    }

    const result = await response.json();
    const content = result.choices[0].message.content;

    // Parse the response
    const needsMoreQueries = content.includes('NEEDS_MORE_QUERIES: YES');
    const additionalQueries = [];

    if (needsMoreQueries) {
      const lines = content.split('\n');
      let inQueriesSection = false;

      for (const line of lines) {
        if (line.includes('ADDITIONAL_QUERIES:')) {
          inQueriesSection = true;
          continue;
        }

        if (inQueriesSection && line.trim()) {
          const match = line.match(/^\d+\.\s*(.+)$/);
          if (match && match[1]) {
            additionalQueries.push(match[1].trim());
          }
        }
      }
    }

    console.log(`Gap analysis: needsMoreQueries=${needsMoreQueries}, additionalQueries=${additionalQueries.length}`);
    return { needsMoreQueries, additionalQueries };

  } catch (error) {
    console.error('Error analyzing content gaps:', error);
    return { needsMoreQueries: false, additionalQueries: [] };
  }
}

// Analyze an individual source and create a summary
async function analyzeIndividualSource(content, query, model, sourceNumber, totalSources) {
  try {
    console.log(`Creating individual analysis for source ${sourceNumber}: ${content.title}`);

    // Smart content management: Extract the most relevant parts while keeping it manageable
    const maxContentLength = 12000; // Increased from unlimited to 12K chars for detailed analysis
    let processedContent = content.content || '';

    // If content is too long, intelligently truncate while preserving structure
    if (processedContent.length > maxContentLength) {
      console.log(`Content length: ${processedContent.length} chars, truncating intelligently to ${maxContentLength} chars`);

      // Try to find query-relevant sections first
      const queryWords = query.toLowerCase().split(' ').filter(word => word.length > 3);
      const paragraphs = processedContent.split('\n').filter(p => p.trim().length > 50);

      // Score paragraphs by relevance to query
      const scoredParagraphs = paragraphs.map(paragraph => {
        const lowerPara = paragraph.toLowerCase();
        const score = queryWords.reduce((acc, word) => {
          return acc + (lowerPara.includes(word) ? 1 : 0);
        }, 0);
        return { paragraph, score, length: paragraph.length };
      });

      // Sort by relevance score, then take as many as fit within limit
      scoredParagraphs.sort((a, b) => b.score - a.score);

      let selectedContent = '';
      let currentLength = 0;

      for (const item of scoredParagraphs) {
        if (currentLength + item.length <= maxContentLength) {
          selectedContent += item.paragraph + '\n\n';
          currentLength += item.length + 2;
        } else {
          break;
        }
      }

      // If we didn't get enough content, fall back to simple truncation
      if (selectedContent.length < maxContentLength * 0.7) {
        selectedContent = processedContent.substring(0, maxContentLength);
      }

      processedContent = selectedContent + '\n\n[Note: Content was intelligently selected/truncated to focus on query-relevant sections]';
      console.log(`Intelligently processed content: ${processedContent.length} chars`);
    }

    const prompt = `You are a research assistant conducting a detailed analysis of a web source for the research query: "${query}"

Source Information:
Title: ${content.title}
URL: ${content.url}

Content:
${processedContent}

IMPORTANT: Your task is to create a DETAILED and COMPREHENSIVE analysis that thoroughly addresses the research query. This analysis will be combined with other source analyses to create a complete research report.

Please provide a detailed analysis that includes:

1. **Source Overview** (3-4 sentences)
   - What type of source this is and its credibility
   - Main topics and scope covered
   - Overall relevance to the research query

2. **Key Findings Related to "${query}"** (detailed section)
   - Extract ALL relevant information that addresses the query
   - Include specific facts, data, statistics, examples
   - Quote important statements or findings
   - Explain the significance of each finding

3. **Detailed Information** (comprehensive section)
   - Provide thorough explanations of concepts mentioned
   - Include any methodologies, processes, or approaches described
   - Note any case studies, examples, or real-world applications
   - Extract any recommendations or best practices mentioned

4. **Supporting Evidence**
   - Any research citations, studies, or expert opinions mentioned
   - Quantitative data or metrics provided
   - Comparative information or benchmarks

5. **Limitations and Context**
   - Note any limitations in the source's coverage
   - Identify any biases or perspectives
   - Mention what aspects of the query are not addressed

CRITICAL REQUIREMENTS:
- Be thorough and detailed - this is NOT a brief summary
- Extract maximum value from this source for the research query
- Include specific details, examples, and data points
- Ensure your analysis directly and comprehensively addresses "${query}"
- Write in clear, well-structured paragraphs with good flow
- Aim for a substantial, informative analysis that provides real research value

Format your response with clear headings and detailed content under each section.`;

    // Create a temporary file for this individual analysis
    const fs = require('fs');
    const os = require('os');
    const path = require('path');
    const tempDir = os.tmpdir();
    const tempFilePath = path.join(tempDir, `individual_analysis_${sourceNumber}_${Date.now()}.txt`);

    // Analyze this individual source
    const analysis = await analyzeWithLLM(prompt, tempFilePath, [content], query, model);

    // Clean up temp file
    try {
      fs.unlinkSync(tempFilePath);
    } catch (cleanupError) {
      console.warn('Could not clean up temp file:', cleanupError.message);
    }

    return analysis;

  } catch (error) {
    console.error(`Error in individual source analysis for source ${sourceNumber}:`, error.message);
    throw error;
  }
}

// Synthesize all individual summaries into a final comprehensive analysis
async function synthesizeAllSummaries(individualSummaries, query, model) {
  try {
    console.log(`Synthesizing ${individualSummaries.length} detailed individual summaries`);

    const summariesText = individualSummaries.map(summary => `
=== SOURCE ${summary.sourceNumber}: ${summary.title} ===
URL: ${summary.url}

DETAILED ANALYSIS:
${summary.summary}

---
`).join('\n');

    const synthesisPrompt = `You are a senior research analyst creating a comprehensive final research report by synthesizing multiple detailed source analyses.

RESEARCH QUERY: "${query}"

I have conducted detailed analyses of ${individualSummaries.length} sources. Below are the comprehensive analyses from each source:

${summariesText}

Your task is to synthesize these detailed analyses into a COMPREHENSIVE, THOROUGH final research report that completely addresses the research query.

REQUIREMENTS FOR YOUR SYNTHESIS:

1. **Executive Summary** (4-5 sentences)
   - Provide a clear, direct answer to the research query
   - Highlight the most important findings across all sources
   - Give an overview of what the research reveals

2. **Comprehensive Findings** (detailed section)
   - Synthesize ALL key information from the source analyses
   - Organize findings into logical themes or categories
   - Include specific data, statistics, examples, and quotes from sources
   - Cross-reference information between sources
   - Highlight consensus and patterns across sources

3. **Detailed Analysis** (thorough section)
   - Provide in-depth analysis of the most important aspects
   - Explain methodologies, processes, or approaches mentioned across sources
   - Include case studies, examples, and real-world applications
   - Discuss implications and significance of findings
   - Address different perspectives or approaches found

4. **Supporting Evidence and Data**
   - Compile quantitative data and metrics from all sources
   - Include research citations and expert opinions mentioned
   - Present comparative information and benchmarks
   - Note any studies or empirical evidence referenced

5. **Contradictions and Limitations**
   - Identify any contradictions or differing viewpoints between sources
   - Note limitations in the research or gaps in coverage
   - Discuss any biases or perspectives that should be considered

6. **Conclusion and Implications**
   - Provide a comprehensive conclusion that addresses the original query
   - Discuss practical implications of the findings
   - Suggest areas for further research if applicable

CRITICAL REQUIREMENTS:
- This should be a SUBSTANTIAL, DETAILED research report, not a brief summary
- Extract and synthesize ALL valuable information from the source analyses
- Ensure comprehensive coverage of the research query "${query}"
- Use clear headings and well-structured paragraphs
- Cite sources appropriately (e.g., "According to [Source Title]...")
- Maintain academic rigor while being accessible
- Aim for a thorough, professional research synthesis

Start your response with "Answer:" followed by your comprehensive research report.`;

    // Create a temporary file for the synthesis
    const fs = require('fs');
    const os = require('os');
    const path = require('path');
    const tempDir = os.tmpdir();
    const tempFilePath = path.join(tempDir, `synthesis_${Date.now()}.txt`);

    // Perform the final synthesis
    const finalAnalysis = await analyzeWithLLM(synthesisPrompt, tempFilePath, [], query, model);

    // Clean up temp file
    try {
      fs.unlinkSync(tempFilePath);
    } catch (cleanupError) {
      console.warn('Could not clean up synthesis temp file:', cleanupError.message);
    }

    console.log('Final comprehensive synthesis completed successfully');
    return finalAnalysis;

  } catch (error) {
    console.error('Error in final synthesis:', error.message);
    throw error;
  }
}

// Fallback function for single query processing (when sub-query generation fails)
async function processSingleQueryResults(data, searchResults) {
  if (!searchResults || searchResults.length === 0) {
    throw new Error('No search results found for this query');
  }

  // Extract content from search results
  mainWindow.webContents.send('log', `Found ${searchResults.length} results, extracting content...`);
  const extractedContents = [];

  for (let i = 0; i < searchResults.length; i++) {
    const result = searchResults[i];
    mainWindow.webContents.send('log', `Extracting content from ${i+1}/${searchResults.length}: ${result.title}`);

    try {
      const content = await extractContentFromUrl(result.url);
      extractedContents.push(content);

      if (i < searchResults.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    } catch (error) {
      console.warn(`Error extracting content from ${result.url}:`, error.message);
      extractedContents.push({
        url: result.url,
        title: result.title || 'Unknown Page',
        content: `Could not extract content: ${error.message}`
      });
    }
  }

  // Continue with normal analysis process
  return { searchResults, extractedContents };
}
